.payout-summary-count.positive .payout-summary-currency { color: var(--color-success); }
/* Global zero-mode enforcement: when an element has class zero, it must not inherit positive/negative coloring */
.zero {
  color: var(--text-secondary, #9aa3ab);
  opacity: 0.9;
}
.zero .positive, .zero .negative, .zero.positive, .zero.negative,
.zero .increase, .zero .decrease, .zero.increase, .zero.decrease,
.zero .up, .zero .down, .zero.up, .zero.down,
.zero .success, .zero .danger, .zero.success, .zero.danger,
.zero .rejected, .zero.rejected {
  color: inherit;
  opacity: inherit;
}
.database-update-btn .btn-icon.btn-icon-white {
  filter: brightness(0) invert(1);
  display: inline-block;
  vertical-align: middle;
}

.database-update-btn span {
  vertical-align: middle;
}

/* ============================================================================
   PRODUCT INSIGHTS PANEL STYLES
   ============================================================================ */

/* Product Insights Panel Overlay */
.product-insights-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9998;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), visibility 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-insights-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* Product Insights Panel */
.product-insights-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 90vw;
  min-width: 1024px;
  height: 100vh;
  background: #F1F3F4;
  border-left: 1px solid var(--border-color);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 9999;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.product-insights-panel.open {
  transform: translateX(0);
}

/* Product Insights Container */
.product-insights-container {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Product Insights Header */
.product-insights-header {
  position: sticky;
  top: 0;
  z-index: 30;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
}

.product-insights-header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-insights-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-insights-icon img {
  width: 16px;
  height: 16px;
  display: block;
}

.product-insights-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 16px;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.197;
}

.product-insights-badge {
  background: linear-gradient(90deg, rgba(0, 171, 190, 0) 0%, rgba(0, 171, 190, 0.1) 100%);
  border: 1.3px solid rgba(0, 171, 190, 0.2);
  border-radius: 6px;
  padding: 4px 8px;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.667;
  color: #00ABBE;
}
.privacy-mode-enabled .product-insights-badge {
  display: none;
}


.product-insights-close {
  width: 32px;
  height: 32px;
  border-radius: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  padding: 0;
}

.product-insights-close:hover {
  background: var(--bg-secondary);
}

.product-insights-close img {
  width: 20px;
  height: 20px;
  display: block;
}

/* Product Insights Divider */
.product-insights-divider {
  width: 100%;
  height: 0.5px;
  background: var(--border-color);
}

/* Product Insights Content */
.product-insights-content {
  flex: 1;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Product Insights Listing Section */
.product-insights-listing-section {
  display: flex;
  gap: 20px;
  align-items: stretch;
  width: 100%;
}

.product-insights-listing-section .listing-analytics-div {
  flex: 1 1 60%;
  min-width: 0;
  background: #FFFFFF;
  border-radius: 8px;
  border: none;
  margin: 0;
  padding: 24px;
}

/* Product Insights Sections */
.product-insights-section {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.product-insights-section > * {
  margin-top: 0;
  margin-bottom: 0;
}

.product-insights-section-header {
  display: flex;
  align-items: center;
}

.product-insights-section-header .product-insights-section-icon {
  flex-shrink: 0;
  margin-right: 10px;
}

.product-insights-title-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.product-insights-section-header .product-insights-section-title {
  text-align: left;
  margin: 0;
}

.product-insights-section-header .view-insights-btn {
  flex-shrink: 0;
  margin-left: auto;
}

.product-insights-section-icon {
  width: 16px;
  height: 16px;
  display: block;
}

.product-insights-section-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  line-height: 1.197;
  color: var(--text-primary);
  margin: 0;
}

.product-insights-section-subtitle {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.197;
  color: var(--text-secondary);
  margin: 0;
}

/* Competition Insights Section */
.competition-insights-section {
  flex: 1 1 40%;
  min-width: 0;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
}

.competition-insights-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.competition-insights-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  text-align: left;
}

.view-on-amazon-btn {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 5px 10px;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.197;
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.2s ease;
  margin-left: auto;
}

.view-on-amazon-btn:hover {
  background: var(--bg-hover);
}

.view-on-amazon-btn img {
  width: 8px;
  height: 8px;
  display: block;
}

/* Sales Insights Tabs (reuse existing snap-chart-filter-tabs styling) */
.product-insights-panel .snap-chart-filter-tabs {
  margin-bottom: 0;
}

/* Sales Insights Grid - matching snap-chart-insights from daily sales chart */
.sales-insights-grid {
  display: flex;
  justify-content: space-between;
  gap: 40px;
}

.sales-insights-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  text-align: left;
}

/* Ad Performance Grid - matching snap-chart-insights style */
.ad-performance-grid {
  display: flex;
  justify-content: space-between;
  gap: 40px;
}

.ad-performance-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  text-align: left;
}

/* Shared insight label and value styles */
.insight-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 11px;
  line-height: 1.197;
  color: var(--text-secondary);
  text-align: left;
}

.insight-value {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  line-height: 1.197;
  color: #000000;
  text-align: left;
}

/* Sales Distribution cards */
.sales-distribution-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
}

.sales-distribution-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.sales-distribution-card-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-insights-panel .sales-distribution-card-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 13px;
  color: var(--text-accent);
}

.product-insights-panel .sales-distribution-card-subtitle {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: var(--text-secondary);
}

.sales-distribution-card-description {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  line-height: 1.4;
  color: var(--text-secondary);
  margin: 0;
}

.sales-distribution-card-body {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sales-distribution-chart {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sales-distribution-summary {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sales-distribution-summary li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  color: var(--text-primary);
}

.sales-distribution-summary .summary-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sales-distribution-summary .summary-value {
  font-weight: 700;
  color: var(--text-accent);
}

.summary-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

/* Daily Sales History */
.daily-sales-history-chart {
  width: 100%;
  min-height: 400px;
  position: relative;
  box-sizing: border-box;
}

#product-insights-daily-sales-chart .snap-chart {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

#product-insights-daily-sales-chart .snap-chart-header {
  margin-bottom: 16px;
}

#product-insights-daily-sales-chart .snap-chart-insights {
  padding: 0;
}

#product-insights-daily-sales-chart .snap-chart-canvas {
  padding: 0;
  background: transparent;
}

[data-theme="dark"] .summary-color {
  border-color: rgba(255, 255, 255, 0.25);
}

/* Dark mode styles */
[data-theme="dark"] .product-insights-panel {
  background: #111216;
  border-left-color: var(--border-color);
}

[data-theme="dark"] .product-insights-header {
  background: var(--bg-primary);
  border-bottom-color: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .product-insights-divider {
  background: var(--border-color);
}

[data-theme="dark"] .product-insights-section {
  background: #1A1D23;
}

[data-theme="dark"] .product-insights-listing-section .listing-analytics-div {
  background: #1A1D23;
}

[data-theme="dark"] .competition-insights-section {
  background: #1A1D23;
}

[data-theme="dark"] .insight-value {
  color: #FFFFFF;
}

[data-theme="dark"] .product-insights-badge {
  color: #00ABBE;
}

[data-theme="dark"] .product-insights-close:hover {
  background: var(--border-color);
}

[data-theme="dark"] .product-insights-close img {
  filter: brightness(0) invert(1);
}

[data-theme="dark"] .view-on-amazon-btn {
  background: transparent;
  border-color: var(--border-color);
}

[data-theme="dark"] .view-on-amazon-btn:hover {
  background: rgba(255, 255, 255, 0.05);
}
:root {
  /* Light theme (default) */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F1F3F4;
  --border-color: #E9EBF2;
  --text-primary: #606F95;
  --text-accent: #1B1D21;
  --btn-hover: #FAFAFA;
  --btn-border: #DCE0E5;

  /* Action Button Variables */
  --action-btn-bg: #470CED;
  --action-btn-hover: #2A00A0;
  --action-btn-disabled: #cfd4d4;
  --action-btn-text: #FFFFFF;

  /* Gradient colors */
  --color-1: 27 92% 53%;    /* Orange: #f77f16 */
  --color-2: 285 96% 50%;   /* Purple: #ab05f9 */
  --color-3: 183 85% 50%;   /* Turquoise: #11e6ec */
  --color-4: 90 3% 93%;     /* Light Gray: #eeefed */

  /* Success Color Variable */
  --color-success: #04AE2C;

  /* Additional Color Variables */
  --color-danger: #FF391F;
  --color-warning: #FDC300;
  --color-info: #470CED;
  --color-light-gray: #F7F8FA;
  --color-medium-gray: #B4B9C5;
  --color-dark-gray: #384459;
  --color-divider: #E9EBF2;

  /* Loaded Files UI Variables */
  --loaded-files-border: var(--color-success);
  --loaded-files-text: var(--text-primary);
  --loaded-files-counter-bg: rgba(4, 174, 44, 0.1);
  --loaded-files-counter-text: var(--color-success);
  --clear-button-bg: rgba(250, 88, 58, 0.05);
  --clear-button-icon: var(--color-danger);
  --loaded-files-progress-bg: rgba(96, 111, 149, 0.1);
  --loaded-files-progress-fill: var(--color-success);

  /* Add transition properties at root level */
  --theme-transition: transform 0.3s ease,
                     opacity 0.3s ease;

  /* Tooltip variables */
  --tooltip-bg: #000000;
  --tooltip-text: #FFFFFF;
  --tooltip-font-size: 12px;
  --tooltip-padding: 8px 16px;
  --tooltip-radius: 6px;
  --tooltip-arrow-size: 5px;
  --tooltip-transition: opacity 0.2s ease, visibility 0.2s ease;

  /* Z-index scale for standardized layering */
  --z-base: 1;
  --z-surface: 10;
  --z-dropdown: 100;
  --z-tooltip: 1000;
  --z-modal: 10000;
  --z-header: 50000;    /* Dashboard header - above modals, below overlays */
  --z-overlay: 100000;
  --z-top: 1000000;
}

[data-theme="dark"] {
  /* Softer Dark Theme 2024 */
  --bg-primary: #1A1D23;      /* Updated dark primary background */
  --bg-secondary: #111216;    /* Slightly lighter background for cards */
  --border-color: #2F3341;    /* Subtle blue-tinted borders */
  --text-primary: #B4B9C5;    /* Soft white-gray for regular text */
  --text-accent: #FFFFFF;     /* Pure white for headings */
  --btn-hover: #292E38;       /* Sidebar button hover color */
  --btn-border: #383C4A;      /* Slightly lighter for buttons */

  /* Action Button Dark Theme */
  --action-btn-disabled: #2F353D;

  /* Modern Gradient Colors - Softer Tones */
  --color-1: 250 70% 60%;     /* Soft Purple-Blue: #7B8CFF */
  --color-2: 200 75% 60%;     /* Ocean Blue: #47A3FF */
  --color-3: 170 65% 60%;     /* Soft Teal: #4ECBC4 */
  --color-4: 220 15% 23%;     /* Blue-Gray: #323645 */

  /* Loaded Files UI Dark Theme */
  --loaded-files-border: var(--color-success);
  --loaded-files-text: var(--text-accent);
  --loaded-files-counter-bg: rgba(4, 174, 44, 0.1);
  --loaded-files-counter-text: var(--color-success);
  --clear-button-bg: rgba(250, 88, 58, 0.1);
  --clear-button-icon: var(--color-danger);
  --loaded-files-progress-bg: rgba(255, 255, 255, 0.1);
  --loaded-files-progress-fill: var(--color-success);

  /* Tooltip Dark Theme */
  --tooltip-bg: #000000;
  --tooltip-text: #FFFFFF;
}

/* Remove transitions from specific elements where needed */
.sidebar-collapse-btn .collapse-icon,
.sidebar.collapsed * {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure smooth transitions for background images and filters */
img {
  transition: var(--theme-transition);
}

/* Add specific transitions for elements that need them */
.sidebar-btn,
.theme-toggle-btn,
.progress-bar,
.metric-item {
  transition: var(--theme-transition);
}

@font-face {
  font-family: 'Amazon Ember';
  src: url('fonts/AmazonEmber_Regular.woff2') format('woff2'),
       url('fonts/AmazonEmber_Regular.woff') format('woff'),
       url('fonts/AmazonEmber_Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Amazon Ember';
  src: url('fonts/Amazon-Ember-Medium.woff2') format('woff2'),
       url('fonts/Amazon-Ember-Medium.woff') format('woff'),
       url('fonts/Amazon-Ember-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Amazon Ember';
  src: url('fonts/AmazonEmber_Bold.woff2') format('woff2'),
       url('fonts/AmazonEmber_Bold.woff') format('woff'),
       url('fonts/AmazonEmber_Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Amazon Ember';
  src: url('fonts/AmazonEmber_He.woff2') format('woff2'),
       url('fonts/AmazonEmber_He.woff') format('woff'),
       url('fonts/AmazonEmber_He.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* Global Styles */
body,
.sidebar,
.sidebar-logo,
.sidebar-nav,
.sidebar-btn,
.main-content {
  font-family: 'Amazon Ember', Arial, sans-serif !important;
}

body {
  margin: 0;
  background: var(--bg-secondary);
  color: var(--text-primary);
  min-width: 1024px;
  overflow-x: auto !important;
  overflow-y: hidden !important;
}

.snapapp-container {
  display: flex;
  height: 100vh; /* Use viewport height instead of min-height */
  min-width: 1024px;
  width: 100%;
  overflow: visible !important;
}

/* Sidebar Styles */
.sidebar {
  width: 276px; /* 28px + 220px + 28px = 276px total */
  min-width: 276px;
  background: var(--bg-primary);
  border-right: 1.5px solid var(--border-color);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 28px; /* 28px left/right padding */
  position: fixed; /* Fix the sidebar */
  top: 0;
  left: 0;
  bottom: 0;
  box-sizing: border-box;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: var(--z-surface); /* Increased from 50 to ensure sidebar stays above main content */
}

/* Replace native border with layered pseudo-element so the half-out icon can cover it */
.sidebar {
  border-right: none;
  position: fixed; /* keep positioning for ::after reference */
}
.sidebar::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1.5px; /* border width */
  height: 100%;
  background: var(--border-color);
  pointer-events: none;
  z-index: var(--z-base); /* lower than collapse button z-index */
}

.sidebar-inner {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  overflow: hidden;
  align-items: flex-start;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-logo {
  width: 220px; /* Updated: 276px sidebar - 56px total padding = 220px */
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 50px;
  position: relative; /* allow absolute positioning of collapse button within logo row */
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Make wordmark white in dark mode */
[data-theme="dark"] .sidebar-logo img[alt="Snap for MOD Logo"] {
  filter: brightness(0) invert(1);
}

.sidebar-logo-icon {
  width: 48px !important;
  height: 48px !important;
  margin-right: 0;
  vertical-align: middle;
}

.sidebar-nav {
  width: 220px; /* Updated: 276px sidebar - 56px total padding = 220px */
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1 1 auto;
  align-items: flex-start;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-btn {
  width: 220px; /* Fixed width for expanded sidebar buttons */
  height: 56px;
  display: flex;
  align-items: center;
  padding: 0 28px 0 16px;
  border-radius: 14px;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  cursor: pointer;
  border: 2px solid transparent;
  background: transparent;
  box-sizing: border-box;
  position: relative;
  white-space: nowrap;
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  justify-content: flex-start;
}

.sidebar-btn .sidebar-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  flex-shrink: 0;
  transition: margin 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-btn span {
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-btn:hover:not(.active):not(.locked):not(:disabled) {
  background: var(--btn-hover);
  color: var(--text-accent);
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* Active state - expanded */
.sidebar-btn.active {
  color: var(--text-accent);
  font-weight: 500;
  border: 1px solid var(--btn-border);
}

/* Dark mode active state */
[data-theme="dark"] .sidebar-btn.active {
  background: var(--btn-hover) !important;
  border: none;
}

/* Collapsed state base */
.sidebar.collapsed .sidebar-btn {
  width: 60px; /* Rectangular buttons: 60px width × 56px height */
  padding: 0 16px;
  justify-content: flex-start;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Collapsed state active */
.sidebar.collapsed .sidebar-btn.active {
  width: 60px; /* Rectangular buttons: 60px width × 56px height */
  padding: 0;
  justify-content: center;
  border: 1px solid var(--btn-border);
}

[data-theme="dark"] .sidebar.collapsed .sidebar-btn.active {
  background: var(--btn-hover) !important;
  border: none;
  padding: 0;
  justify-content: center;
}

/* Hide text in collapsed state */
.sidebar.collapsed .sidebar-btn span {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: opacity;
}

/* Remove margin from icon in collapsed state */
.sidebar.collapsed .sidebar-btn .sidebar-icon {
  margin-right: 0;
  transition: margin 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Transitions only for non-layout properties */
.sidebar-btn {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Removed background-color, color, and border-color transitions to prevent flashing on theme change */
}

.sidebar {
  /* Removed transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); */
}

.sidebar-btn span {
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-btn.locked {
  opacity: 1;
  cursor: not-allowed;
  background: none;
  color: var(--color-divider);
  font-weight: 500;
  border: none;
}

.sidebar-btn.active .sidebar-icon {
  /* Using dedicated active state icon instead of filter */
}

.sidebar-btn:hover:not(.active) .sidebar-icon {
  filter: none !important;
}

/* Add styles for disabled sidebar buttons */
.sidebar-btn:disabled {
  cursor: not-allowed;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
}

.sidebar-btn:disabled span,
.sidebar-btn.locked span,
.sidebar-btn:disabled .sidebar-icon,
.sidebar-btn.locked .sidebar-icon {
  color: var(--text-primary) !important;
  opacity: 0.45;
}

[data-theme="dark"] .sidebar-btn:disabled span,
[data-theme="dark"] .sidebar-btn.locked span,
[data-theme="dark"] .sidebar-btn:disabled .sidebar-icon,
[data-theme="dark"] .sidebar-btn.locked .sidebar-icon {
  color: var(--text-primary) !important;
  opacity: 0.45;
}

/* Remove tooltip for disabled buttons */
.sidebar-btn:disabled::after {
  display: none !important;
}

.sidebar.collapsed .sidebar-btn:disabled::after {
  display: none !important;
}

/* Locked button styles */
.sidebar-btn.locked {
  position: relative;
  opacity: 1;
  cursor: not-allowed;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Make lock icon container fully visible */
.lock-icon-container {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--color-danger);
  border-radius: 12px;
  z-index: var(--z-tooltip);
  filter: none !important;
  opacity: 1 !important;
  padding: 0;
  box-sizing: border-box;
}

/* The lock icon itself */
.lock-icon {
  width: 14px;
  height: 14px;
  color: var(--tooltip-text);
  display: block; /* Remove any potential inline spacing */
  margin: 0; /* Remove any margins */
}

/* Apply a CSS filter hack to counteract parent opacity */
.sidebar-btn.locked .lock-icon-container {
  filter: contrast(1.5) brightness(1.3) !important; /* Make it fully visible */
  opacity: 1 !important;
  z-index: var(--z-tooltip); /* Above the tooltip */
}

/* Remove old tooltip styles */
.sidebar-btn.locked::before,
.sidebar-btn.locked::after {
  display: none;
}

/* Add new tooltip styles */
.tooltip {
  /* Force tooltip to be single-line */
  white-space: nowrap;
  width: auto;
  max-width: none;
  min-width: unset;
  position: absolute;
  top: 50%;
  left: calc(100% + 12px);
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  background: var(--color-danger);
  color: var(--tooltip-text);
  font-size: 13px;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  pointer-events: none;
  z-index: var(--z-tooltip);
  opacity: 0;
  transition: opacity 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  gap: 8px;
}

.tooltip img {
  width: 14px;
  height: 14px;
}

.sidebar-btn.locked:hover .tooltip {
  opacity: 1;
}

/* Arrow for tooltip */
.tooltip::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 100%;
  transform: translateY(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: transparent var(--color-danger) transparent transparent;
  margin: 0;
  width: 0;
  height: 0;
}

/* Position tooltip for collapsed sidebar */
.sidebar.collapsed .tooltip {
  left: calc(100% + 24px);
}

/* Position arrow for collapsed sidebar */
.sidebar.collapsed .tooltip::after {
  right: 100%;
  margin-right: 0;
}

.sidebar-collapse-btn {
  position: absolute; /* anchor within .sidebar-logo row */
  right: 0; /* flush to the right edge inside the .sidebar-logo container */
  top: 50%;
  transform: translateY(-50%);
  width: 20px; /* icon-only */
  height: 20px; /* icon-only */
  background: transparent;
  border: none;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: none;
  z-index: var(--z-tooltip); /* above sidebar ::after border */
  padding: 0;
  transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* match sidebar width transition */
}

/* Dark mode collapse button - icon only, no background/border */
[data-theme="dark"] .sidebar-collapse-btn {
  background: transparent;
  border: none;
  box-shadow: none;
}

/* Hover state for both collapsed and uncollapsed */
.sidebar-collapse-btn:hover {
  box-shadow: none;
}

/* Dark mode hover state */
[data-theme="dark"] .sidebar-collapse-btn:hover {
  box-shadow: none;
}

/* Collapse icon */
.collapse-icon {
  width: 20px;
  height: 20px;
  position: relative; /* allow z-index */
  z-index: var(--z-tooltip); /* above local mask pseudo-element */
}

/* Optional subtle hover scale */
.sidebar-collapse-btn:hover .collapse-icon {
  transform: scale(1.06);
}

/* When sidebar is collapsed, show the uncollapse icon half outside */
.sidebar.collapsed .sidebar-collapse-btn {
  right: -38px; /* 28px sidebar right padding + 10px half icon width */
  z-index: var(--z-tooltip); /* ensure above sidebar border pseudo-element */
}

/* Mask the sidebar border behind the half-out icon using a pseudo-element */
.sidebar.collapsed .sidebar-collapse-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 28px; /* slightly larger than 20px icon to fully cover border */
  height: 28px;
  background: var(--bg-primary); /* match page background to hide border */
  border-radius: 6px;
  pointer-events: none;
  z-index: var(--z-base); /* below the icon image but above the sidebar border */
}

/* No rotation needed since we swap icons */

.sidebar.collapsed {
  width: 116px; /* 28px + 60px + 28px = 116px total */
  min-width: 116px;
  padding: 28px; /* 28px left/right padding */
  align-items: flex-start;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1), min-width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Removed background/color transitions to prevent flashing on theme change */
}

.sidebar.collapsed .sidebar-inner {
  width: 60px; /* Match nav and logo width - content stays within left padding area */
  align-items: flex-start; /* Align content to start */
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: visible; /* allow collapse button to protrude half outside */
}

.sidebar.collapsed .sidebar-logo {
  width: 60px; /* Match nav width */
  min-width: 60px;
  margin-bottom: 50px;
  justify-content: flex-start; /* Left-align logo icon to match nav icons alignment */
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1), min-width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Removed background/color transitions to prevent flashing on theme change */
}

.sidebar.collapsed .sidebar-logo img:not(:first-child) {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.collapsed .sidebar-btn::after {
  content: attr(data-label);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: #222;
  color: #fff;
  font-size: 13px;
  font-weight: 400;
  padding: 4px 10px;
  border-radius: 6px;
  opacity: 0;
  pointer-events: none;
  white-space: nowrap;
  margin-left: 8px;
  z-index: var(--z-tooltip);
  transition: opacity 0.2s;
}

.sidebar.collapsed .sidebar-btn:hover::after {
  opacity: 1;
}

/* Dark mode styles for collapsed sidebar button tooltips */
[data-theme="dark"] .sidebar.collapsed .sidebar-btn::after {
  background: #FFFFFF;
  color: #1E2028;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.sidebar.collapsed .sidebar-nav {
  width: 60px; /* Exact button width - nav container matches button width */
  min-width: 60px;
  align-items: flex-start; /* Align buttons to start of container */
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1), min-width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Removed background/color transitions to prevent flashing on theme change */
}

.sidebar.collapsed .sidebar-btn,
.sidebar.collapsed .sidebar-btn.active,
.sidebar.collapsed .sidebar-btn:hover {
  width: 60px; /* Rectangular buttons: 60px width × 56px height */
  padding: 0 16px;
  justify-content: flex-start;
  margin: 0;
}

.sidebar.collapsed .sidebar-btn span {
  display: none !important;
}

.sidebar-icon {
  width: 28px;
  height: 28px;
  margin-right: 12px;
  vertical-align: middle;
}

/* Main content styles */
.main-content {
  flex: 1;
  padding: 28px 16px 28px 28px; /* Left padding matches sidebar's 28px padding */
  display: flex;
  flex-direction: column;
  gap: 32px;
  width: 100%;
  box-sizing: border-box;
  min-width: 1024px;
  margin-left: 276px !important; /* Expanded sidebar width (28px + 220px + 28px = 276px) */
  height: 100vh;
  overflow-x: visible !important;
  overflow-y: auto; /* Enable scrolling for main content */
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: var(--z-base); /* Reduced from 100 to ensure main content stays below sidebar */
}

/* Add global header styles */
.main-content h1 {
  font-size: 24px;
  margin: 0;
  /* Match dashboard header h1 vertical position */
  margin-top: 10px;
  font-weight: 500;
  color: var(--text-accent);
}

/* Remove max-width constraints */
@media (min-width: 1024px) {
  .sidebar {
    width: 276px; /* 28px + 220px + 28px = 276px total */
    padding: 28px; /* 28px left/right padding */
  }
  .sidebar-logo {
    margin-bottom: 50px;
  }
}

@media (min-width: 1440px) {
  .main-content {
    padding: 28px 16px 28px 28px; /* Adjusted to match new padding values */
    width: 100%;
  }
}

/* Theme toggle styles */
.theme-toggle-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: auto;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-toggle-btn {
  width: 100px;
  height: 43px;
  background: #E8EBF4;
  border-radius: 28.82px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 2.88px;
  position: relative;
  gap: 2.88px;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

[data-theme="dark"] .theme-toggle-btn {
  background: #292E38;
}

.theme-toggle-btn .toggle {
  width: 45.68px;
  height: 37.24px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  border-radius: 23.05px;
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  left: 2.88px;
}

.theme-toggle-btn .toggle:last-child {
  left: 51.44px;
}

.theme-toggle-btn .toggle.active {
  background: #FFFFFF;
  z-index: var(--z-base);
}
[data-theme="dark"] .theme-toggle-btn .toggle.active {
  background: #1B1D21;
}

/* Collapsed state - Keep transitions for collapse/expand */
.sidebar.collapsed .theme-toggle-container {
  width: 60px; /* Match nav and logo width */
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.collapsed .theme-toggle-btn {
  width: 43px;
  height: 43px;
  padding: 2.88px;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              padding 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.collapsed .theme-toggle-btn .toggle {
  width: 37.24px;
  height: 37.24px;
  left: 2.88px;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.collapsed .toggle:not(.active) {
  opacity: 0;
  visibility: hidden;
  transform: translateX(-100%);
}

.sidebar.collapsed .toggle.active {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

/* Remove transitions only for theme switching in collapsed state */
[data-theme="dark"] .sidebar.collapsed .theme-toggle-btn,
[data-theme="light"] .sidebar.collapsed .theme-toggle-btn {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              padding 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] .sidebar.collapsed .toggle,
[data-theme="light"] .sidebar.collapsed .toggle {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] .sidebar.collapsed .toggle.active,
[data-theme="light"] .sidebar.collapsed .toggle.active {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] .sidebar.collapsed .toggle:not(.active),
[data-theme="light"] .sidebar.collapsed .toggle:not(.active) {
  transition: none;
}

.theme-icon {
  width: 16px;
  height: 16px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-icon img {
  width: 16px;
  height: 16px;
  color: #384459;
}

/* Make all icons white in dark mode */
[data-theme="dark"] .theme-toggle-btn .theme-icon img {
  color: #FFFFFF;
  filter: brightness(0) invert(1);
}

/* Ensure icons in active toggle are properly colored */
[data-theme="dark"] .theme-toggle-btn .toggle.active .theme-icon img {
  color: #FFFFFF;
  filter: brightness(0) invert(1);
}

/* Global utility classes */
img {
  user-drag: none;
  -webkit-user-drag: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: auto;
}

/* Position lock icon in collapsed mode */
.sidebar.collapsed .lock-icon-container {
  right: 0;
  top: auto;
  bottom: 0;
  transform: none;
  z-index: var(--z-tooltip);
}

/* Adjust margin when sidebar is collapsed */
.sidebar.collapsed + .main-content,
.sidebar.collapsed ~ .main-content {
  margin-left: 116px !important; /* Collapsed sidebar width (28px + 60px + 28px = 116px) */
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Action Button */
.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 42px;
  padding: 0 24px;
  border: none;
  border-radius: 6px;
  background: var(--action-btn-bg);
  cursor: pointer;
  transition: var(--theme-transition);
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--action-btn-text);
}

.action-button:hover:not(:disabled) {
  background: var(--action-btn-hover);
}

.action-button:disabled {
  background: var(--action-btn-disabled);
  opacity: 0.8;
  cursor: not-allowed;
}

.action-button img {
  width: 12px;
  height: 12px;
  filter: brightness(0) invert(1);
  transition: var(--theme-transition);
}

.action-button:disabled img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
  opacity: 0.8;
}

/* Full width variant */
.action-button.full-width {
  width: 100%;
}

/* Global Tooltip Styles */
[data-tooltip] {
  position: relative;
}

[data-tooltip]:before {
  /* Force tooltip to be single-line and auto-size properly */
  white-space: nowrap !important;
  width: auto !important;
  max-width: none !important;
  min-width: auto !important;
  content: attr(data-tooltip);
  position: fixed !important;
  bottom: var(--tooltip-bottom, calc(100% + 8px));
  left: var(--tooltip-left, 50%);
  transform: translateX(-50%);
  padding: var(--tooltip-padding) !important;
  background: var(--tooltip-bg) !important;
  color: var(--tooltip-text) !important;
  font-family: 'Amazon Ember', sans-serif;
  font-size: var(--tooltip-font-size) !important;
  font-weight: 500;
  border-radius: var(--tooltip-radius) !important;
  line-height: 1.4; /* Add more vertical room */
  opacity: 1 !important;
  visibility: hidden;
  transition: var(--tooltip-transition);
  z-index: var(--z-tooltip) !important;
  pointer-events: none;
  /* Ensure proper text measurement */
  display: inline-block !important;
  text-align: center !important;
}



[data-tooltip]:after {
  content: '';
  position: fixed !important;
  bottom: var(--tooltip-bottom, calc(100% + 10px));
  left: var(--tooltip-left, 50%);
  transform: translateX(-50%);
  border-left: var(--tooltip-arrow-size, 5px) solid transparent;
  border-right: var(--tooltip-arrow-size, 5px) solid transparent;
  border-top: var(--tooltip-arrow-size, 5px) solid var(--tooltip-bg);
  opacity: 1 !important;
  visibility: hidden;
  transition: var(--tooltip-transition);
  z-index: var(--z-tooltip) !important;
  pointer-events: none;
}

[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
  opacity: 1 !important;
  visibility: visible;
}

/* Dark theme adjustments */
[data-theme="dark"] [data-tooltip]:before {
  background: var(--tooltip-bg) !important;
  color: var(--tooltip-text) !important;
}

[data-theme="dark"] [data-tooltip]:after {
  border-top-color: var(--tooltip-bg) !important;
}

/* Ensure parent containers don't mask tooltips */
/* Note: Removed overflow: visible from all [data-tooltip] elements to fix deprecation warning */
/* Only apply overflow: visible to tooltip elements that don't contain media */
[data-tooltip]:not(img):not(video):not(canvas) {
  /* Use position and z-index instead of overflow for tooltip positioning */
  position: relative;
  z-index: var(--z-base);
}

/* For media elements with tooltips, ensure proper stacking without overflow issues */
img[data-tooltip],
video[data-tooltip],
canvas[data-tooltip] {
  position: relative;
  z-index: var(--z-base);
}

/* Fix tooltips showing with text cursor instead of pointer */
input[data-tooltip],
textarea[data-tooltip] {
  cursor: pointer !important;
}

/* Prevent tooltip clipping by parent containers (but preserve scroll containers) */
/* Note: Removed overflow: visible from containers with img/video/canvas elements to fix deprecation warning */
.marketplaces-div,
.marketplace-col,
.sales-cards-container,
.dashboard-component,
.account-status,
.listings-status,
.ad-spend,
.database-container {
  overflow: visible !important;
}

/* Apply overflow: visible only to containers that don't contain media elements */
.listing-analytics-div,
.listing-middle-div,
.listing-badges-row {
  /* Removed overflow: visible to fix Chrome deprecation warning for containers with img elements */
  position: relative; /* Ensure tooltips can still position correctly */
}

/* For specific tooltip-containing elements, use overflow: visible carefully */
.listing-badge:not(:has(img)),
.listing-ad-row:not(:has(img)),
.listing-edit-analyse-row:not(:has(img)) {
  overflow: visible !important;
}

/* For elements that definitely contain images, ensure proper containment */
.listing-analyse-ic,
.listing-edit-ic {
  /* Removed overflow: visible to fix Chrome deprecation warning */
  position: relative;
}

/* Allow main-content to scroll vertically but not clip tooltips horizontally */
.main-content {
  overflow-x: visible !important;
  overflow-y: auto !important;
}

/* Allow sales card divs to not clip tooltips horizontally */
.todays-sales-card-div,
.yesterdays-sales-card-div,
.current-month-card-div,
.last-month-card-div,
.current-year-card-div,
.last-year-card-div {
  overflow-x: visible !important;
}

/* Allow sales-scrollable-content to scroll vertically but not clip tooltips horizontally */
.sales-scrollable-content {
  overflow-x: visible !important;
  overflow-y: auto !important;
}

/* Force tooltips to be on top of everything */
.fit-type-tooltip,
.ad-spend-tooltip,
.ordered-colors-tooltip,
[data-tooltip]:before,
[data-tooltip]:after {
  z-index: var(--z-tooltip) !important;
  position: fixed !important;
}

/* Ensure tooltip containers can extend beyond their bounds without affecting media elements */
.listing-analytics-div [data-tooltip],
.listing-middle-div [data-tooltip],
.listing-badges-row [data-tooltip],
.listing-badge [data-tooltip],
.listing-ad-row [data-tooltip],
.listing-edit-analyse-row [data-tooltip],
.listing-analyse-ic [data-tooltip],
.listing-edit-ic [data-tooltip] {
  /* Use transform to ensure tooltips can appear outside container bounds */
  transform: translateZ(0);
}

/* Ensure parent containers don't interfere with tooltip sizing */
/* Note: Removed overflow: visible from image-containing elements to fix deprecation warning */
.listing-edit-analyse-row,
.listing-analyse-ic,
.listing-edit-ic {
  position: relative !important;
  /* Use z-index to ensure tooltips appear above other elements */
  z-index: var(--z-base);
}

.sidebar-btn.locked span {
  opacity: 0.5 !important;
}
[data-theme="dark"] .sidebar-btn.locked span {
  opacity: 0.5 !important;
}

.sidebar:not(.collapsed) .sidebar-btn span {
  opacity: 1;
  pointer-events: auto;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline;
}

.sidebar-logo img.wordmark {
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.sidebar.collapsed .sidebar-logo img.wordmark {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Tip Card Styles */
.main-content {
  position: relative;
}
.tip-card {
  position: static;
  left: auto;
  right: auto;
  bottom: auto;
  margin-top: auto;
  margin-bottom: 0;
  background: var(--bg-primary);
  border-radius: 14px;
  padding: 32px;
  display: flex;
  flex-direction: column;
  gap: 32px;
  max-width: 460px;
  width: auto;
  box-sizing: border-box;
  margin-left: 0;
  margin-right: 0;
  border: 1.5px solid var(--border-color);
  z-index: var(--z-surface);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
}

.tip-card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.tip-card-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 18px;
  line-height: 1.2em;
  color: var(--text-accent);
}

.tip-card-description {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4em;
  color: var(--text-primary);
}

.tip-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* Use our existing checkbox styles */
.tip-card .checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.tip-card .checkbox-wrapper img {
  width: var(--checkbox-size);
  height: var(--checkbox-size);
}

/* Global checkbox styling for unchecked state */
.checkbox-wrapper img[src*="uncheckedbox-ic"] {
  opacity: 0.2;
}

[data-theme="dark"] .checkbox-wrapper img[src*="uncheckedbox-ic"],
body.dark .checkbox-wrapper img[src*="uncheckedbox-ic"] {
  opacity: 1 !important;
}

/* Compare checkbox styling for unchecked state */
.compare-checkbox img[src*="uncheckedbox-ic"] {
  opacity: 0.2;
}

[data-theme="dark"] .compare-checkbox img[src*="uncheckedbox-ic"],
body.dark .compare-checkbox img[src*="uncheckedbox-ic"] {
  opacity: 1 !important;
}

/* Show/Hide Options checkbox styling for unchecked state - match compare dropdown styling */
.show-hide-options-checkbox img[src*="uncheckedbox-ic"] {
  opacity: 0.2;
}

[data-theme="dark"] .show-hide-options-checkbox img[src*="uncheckedbox-ic"],
body.dark .show-hide-options-checkbox img[src*="uncheckedbox-ic"] {
  opacity: 1 !important;
}

.tip-card .checkbox-wrapper label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.67em;
  color: var(--text-primary);
  cursor: pointer;
}

.tip-card-navigation {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Figma-style navigation buttons */
.tip-card-navigation button {
  height: 36px;
  padding: 0 24px;
  border-radius: 6px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

.tip-card-navigation button.prev {
  background: transparent;
  border: 1px solid var(--btn-border);
  color: var(--text-primary);
}

.tip-card-navigation button.next,
.tip-card-navigation button.done {
  background: var(--action-btn-bg);
  border: none;
  color: var(--action-btn-text);
}

.tip-card-navigation button.prev:hover {
  border-color: var(--btn-border);
  background: var(--btn-hover);
}

.tip-card-navigation button.next:hover,
.tip-card-navigation button.done:hover {
  background: var(--action-btn-hover);
}

/* Dark theme overrides */
[data-theme="dark"] .tip-card {
  box-shadow: none;
}

@media (max-width: 600px) {
  .tip-card {
    left: var(--main-content-padding-mobile, 16px);
    right: var(--main-content-padding-mobile, 16px);
    bottom: var(--main-content-padding-mobile, 16px);
    padding: 16px;
    max-width: 100%;
  }
}

/* Dashboard Component Styles */
.dashboard-component {
  padding: 0 0 20px 0;
  width: 100%;
  min-width: 0; /* Prevent flex item from overflowing */
}

.account-status {
  width: 100%;
  min-height: 92px;
  background: var(--bg-primary);
  border-radius: 14px;
  padding: 24px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.account-status-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 30px;
  margin-bottom: 32px;
  width: 100%;
  min-width: 0;
}

.account-status-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.account-status-title span {
  display: flex;
  align-items: center;
  height: 16px;
  line-height: 16px;
  color: #606F95;
  margin-top: 2px;
}

.account-status-icon {
  width: 16px;
  height: 16px;
}

.account-status-title span {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 10px;
}

.account-status-metrics {
  display: grid;
  grid-template-columns: max-content 1fr;
  gap: 24px;
  width: 100%;
  min-width: 0;
  align-items: center; /* Vertically align children */
}

.account-status-left { display: flex; flex-direction: column; justify-content: center; width: fit-content; }

.account-metrics-section {
  display: grid;
  grid-template-columns: 3fr 4fr 3fr; /* 30% / 40% / 30% as proportional fr units to keep within container with gaps */
  gap: 24px;
  align-items: center;
  width: 100%;
  min-width: 0; /* allow grid children to shrink within the available space */
}

.account-metrics-section > .metric-item { min-width: 0; }

.account-status-tier {
  position: relative;
  min-width: 0;
  height: 100%;
  padding-left: 0;
  display: flex;
  flex-direction: column;
  justify-content: center; /* vertically center content within parent */
}

.tier-info {
  display: flex;
  flex-direction: column;
}

/* Tier top row: icon + label inline */
.tier-top { display: flex; align-items: center; gap: 6px; }

.tier-icon { width: 16px; height: 16px; display: inline-block; margin-bottom: 0; }

/* Reuse the animated gradient like gradient-text, but mask to the SVG shape */
.gradient-icon {
  background: linear-gradient(
    45deg,
    #ff00fa 0%,
    #ff00fa 15%,
    #fc084e 35%,
    #ff9100 65%,
    #ff00fa 85%,
    #ff0062 100%
  );
  background-size: 200% 100%;
  animation: gradient-shift 8s ease-in-out infinite alternate;
  -webkit-mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 26"><path fill="%23606f95" d="M24,17.1v-8.1c0-1.4-.7-2.7-1.9-3.4L14.1.6c-1.3-.8-2.9-.8-4.2,0L1.9,5.5c-1.2.7-1.9,2-1.9,3.4v8.1c0,1.4.7,2.7,1.9,3.4l8,4.9c1.3.8,2.9.8,4.2,0l8-4.9c1.2-.7,1.9-2,1.9-3.4h0ZM17.7,12.6l-2.1,2.1.5,2.9c0,.5-.3,1.1-.8,1.1s-.4,0-.6-.1l-2.6-1.4-2.6,1.4c-.5.3-1.1,0-1.4-.4,0-.2-.1-.4,0-.6l.5-2.9-2.1-2.1c-.4-.4-.4-1,0-1.4.2-.1.3-.2.6-.3l2.9-.4,1.3-2.7c.3-.5.9-.7,1.4-.4.1,0,.3.2.4.4l1.3,2.7,2.9.4c.5,0,.9.6.8,1.1,0,.2-.1.4-.3.6h0Z"/></svg>') no-repeat left top;
  mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 26"><path fill="%23606f95" d="M24,17.1v-8.1c0-1.4-.7-2.7-1.9-3.4L14.1.6c-1.3-.8-2.9-.8-4.2,0L1.9,5.5c-1.2.7-1.9,2-1.9,3.4v8.1c0,1.4.7,2.7,1.9,3.4l8,4.9c1.3.8,2.9.8,4.2,0l8-4.9c1.2-.7,1.9-2,1.9-3.4h0ZM17.7,12.6l-2.1,2.1.5,2.9c0,.5-.3,1.1-.8,1.1s-.4,0-.6-.1l-2.6-1.4-2.6,1.4c-.5.3-1.1,0-1.4-.4,0-.2-.1-.4,0-.6l.5-2.9-2.1-2.1c-.4-.4-.4-1,0-1.4.2-.1.3-.2.6-.3l2.9-.4,1.3-2.7c.3-.5.9-.7,1.4-.4.1,0,.3.2.4.4l1.3,2.7,2.9.4c.5,0,.9.6.8,1.1,0,.2-.1.4-.3.6h0Z"/></svg>') no-repeat left top;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-position: left top;
  mask-position: left top;
}

.tier-label {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 600;
  font-size: 12px;
  line-height: 10px;
}

.tier-value {
  color: var(--text-accent);
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 800;
  font-size: 24px;
  line-height: 1.2;
  margin-top: 10px;
  position: relative;
  z-index: var(--z-base);
  padding-bottom: 0;
}

.metric-item {
  position: relative;
  min-width: 0;
  height: 100%;
  padding-left: 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 25%;
  width: 1px;
  height: 50%;
  background: #606F95;
  opacity: 0.1;
  transition: var(--theme-transition);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  order: 1;
}

.metric-label {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 10px;
}

/* Override sizes for account metrics labels only */
.account-metrics-section .metric-label {
  font-size: 12px;
  font-weight: 600;
}

.metric-percentage {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 8px;
}

.progress-bar {
  position: relative;
  height: 6px !important; /* Force height to be consistent */
  min-height: 6px; /* Ensure minimum height */
  max-height: 6px; /* Ensure maximum height */
  width: 100%;
  border-radius: 10px;
  overflow: hidden;
  margin: 0;
  order: 2;
}

.progress-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #F7F8FA;
  border-radius: 10px;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  /* Width is controlled by JavaScript inline styles */
  background: var(--loaded-files-progress-fill);
  border-radius: 10px;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.metric-subtext {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 11px;
  line-height: 1.4;
  margin: 0;
  margin-top: 4px;
  order: 3;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 4px;
}

.metric-remaining {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 600;
  font-size: 10px;
  line-height: 1.4;
  margin-left: auto;
  position: relative;
  flex-shrink: 0;
}

[data-theme="dark"] .account-status {
  background: var(--bg-primary);
}

[data-theme="dark"] .progress-track {
  background: var(--border-color);
}

[data-theme="dark"] .progress-fill {
  background: var(--loaded-files-progress-fill);
}
[data-theme="dark"] .metric-item:not(:first-child)::before,
[data-theme="dark"] .metric-item::before {
  background: #B4B9C5;
  opacity: 0.1;
}

[data-theme="dark"] .metric-label,
[data-theme="dark"] .metric-percentage,
[data-theme="dark"] .metric-subtext,
[data-theme="dark"] .metric-remaining,
[data-theme="dark"] .tier-label,
[data-theme="dark"] .account-status-title span {
  color: #FFFFFF;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 200% 50%;
  }
}

.gradient-text {
  position: relative;
  display: inline-block;
  color: transparent;
  background: linear-gradient(
    45deg,
    #ff00fa 0%,
    #ff00fa 15%,
    #fc084e 35%,
    #ff9100 65%,
    #ff00fa 85%,
    #ff0062 100%
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  animation: gradient-shift 8s ease-in-out infinite alternate;
  padding: 5px 0 0 0; /* 5px top padding, others 0, using px units */
  margin: 0;
  line-height: 1.2;
  height: auto;
  overflow: visible;
  border-radius: 0;
}

@media (min-width: 1024px) {
  .metric-item {
    padding-left: 24px;
  }
}

/* Listings Status Styles (Figma-inspired) */
.listings-status {
  width: 100%;
  min-height: 92px;
  background: var(--bg-primary);
  border: 1.5px solid #E9EBF2;
  border-radius: 14px;
  padding: 24px;
  margin-top: 32px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.listings-status-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 30px;
  margin-bottom: 32px;
  width: 100%;
  min-width: 0;
}

.listings-status-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.listings-status-icon {
  width: 16px;
  height: 16px;
}

.listings-status-title span {
  color: #470CED;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 10px;
}

.listings-status-metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  width: 100%;
  min-width: 0;
}

.listings-metric-item {
  position: relative;
  min-width: 0;
  height: 100%;
  padding-left: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: none;
}

.listings-metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.listings-metric-label {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 10px;
}

.listings-metric-value {
  color: var(--text-accent);
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 800;
  font-size: 18px;
  line-height: 1.2;
}

.listings-metric-subtext {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 8px;
  margin: 0;
  margin-top: 4px;
}

[data-theme="dark"] .listings-status {
  background: var(--bg-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .listings-metric-label,
[data-theme="dark"] .listings-metric-value,
[data-theme="dark"] .listings-metric-subtext,
[data-theme="dark"] .listings-status-title span {
  color: #FFFFFF;
}

@media (max-width: 900px) {
  .listings-status-metrics {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
}

@media (max-width: 600px) {
  .listings-status {
    padding: 12px;
  }
  .listings-status-header {
    margin-bottom: 16px;
  }
  .listings-status-metrics {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* Listings Status Overview (Figma-accurate) */
.listings-status-overview {
  width: 100%;
  background: var(--bg-primary);
  border-radius: 14px;
  padding: 24px;
  margin-top: 16px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.listings-status-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.listing-status-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  box-sizing: border-box;
  width: auto;
  gap: 4px;
}

.listing-status-top {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.listing-status-number {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  line-height: 1.2;
  color: #606F95;
}
.listing-status-number.live {
  color: #04AE2C;
}
.listing-status-number.zero {
  color: #606F95; /* gray like other metrics */
  opacity: 0.4;
}
[data-theme="dark"] .listing-status-number {
  color: #606F95;
}
[data-theme="dark"] .listing-status-number.zero {
  color: #B4B9C5; /* zero gray in dark theme */
  opacity: 0.4;
}
.listing-status-number.rejected {
  color: #FF391F;
}

.listing-status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12px;
  height: 12px;
}

.listing-status-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.2;
  color: #606F95;
  margin-top: 2px;
  letter-spacing: 0.02em;
}

.listing-status-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1px;
  height: 35px;
  background: #606F95;
  opacity: 0.1;
  margin: 0 8px;
}

[data-theme="dark"] .listings-status-overview {
  background: var(--bg-primary);
}
[data-theme="dark"] .listing-status-number,
[data-theme="dark"] .listing-status-label {
  color: #B4B9C5;
}
[data-theme="dark"] .listing-status-number.live {
  color: #04AE2C;
}
[data-theme="dark"] .listing-status-number.zero {
  color: #B4B9C5;
}
[data-theme="dark"] .listing-status-number.rejected {
  color: #FF391F;
}
[data-theme="dark"] .listing-status-divider svg rect {
  fill: #B4B9C5;
  fill-opacity: 0.1;
}

@media (max-width: 900px) {
  .listings-status-row {
    flex-wrap: wrap;
    gap: 8px;
  }
  .listing-status-card {
    min-width: 120px;
    padding: 8px 8px;
  }
}
@media (max-width: 600px) {
  .listings-status-overview {
    padding: 12px;
  }
  .listings-status-row {
    flex-direction: column;
    gap: 0;
  }
  .listing-status-card {
    flex: none;
    min-width: 0;
    width: 100%;
    padding: 8px 0;
    border-bottom: 1px solid #E9EBF2;
  }
  .listing-status-card:last-child {
    border-bottom: none;
  }
  .listing-status-divider {
    display: none;
  }
}

.snap-dropdown {
    position: relative;
    cursor: pointer;
    user-select: none;
}
.snap-dropdown .dropdown-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    height: 40px;
    border: 1.5px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    box-sizing: border-box;
}
.snap-dropdown.focused .dropdown-header {
    border-color: #470CED;
}
.snap-dropdown .dropdown-header span {
    padding-left: 12px;
    color: var(--text-primary);
}
.snap-dropdown .dropdown-header img {
    margin-right: 12px;
}
.snap-dropdown .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: var(--bg-primary);
    border: 1.5px solid var(--border-color);
    border-radius: 8px;
    margin-top: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: var(--z-dropdown) !important; /* Standardized z-index for dropdown */
    box-sizing: border-box;
}
.snap-dropdown .dropdown-menu:not(.hidden) {
    display: block;
}
.snap-dropdown .dropdown-list {
    max-height: 200px;
    overflow-y: auto;
    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: #D9DDEB transparent;
}

/* Webkit scrollbar styling for dropdown list */
.snap-dropdown .dropdown-list::-webkit-scrollbar {
    width: 6px;
}

.snap-dropdown .dropdown-list::-webkit-scrollbar-track {
    background: transparent;
}

.snap-dropdown .dropdown-list::-webkit-scrollbar-thumb {
    background: #D9DDEB;
    border-radius: 3px;
}

.snap-dropdown .dropdown-list::-webkit-scrollbar-thumb:hover {
    background: #E9EBF2;
}
.snap-dropdown .dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
}
.snap-dropdown .dropdown-item:hover {
    background: #F3F4F6;
}
.snap-dropdown .dropdown-item.selected {
    background: #F3F4F6;
    color: #470CED;
}
.snap-dropdown .search-input {
    width: 100%;
    border: none;
    outline: none;
    font-size: 12px;
    color: #1F2937;
    padding: 0;
    background: transparent;
}
.snap-dropdown .search-input::placeholder {
    color: var(--color-text-placeholder);
}

.database-container {
  width: 100%;
  margin-top: 32px;
  margin-bottom: 0;
  background: transparent;
  box-sizing: border-box;
  /* overflow: hidden; */
  position: relative;
  z-index: var(--z-modal); /* Standardized z-index for modal-level elements */
}
.database-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 32px; /* Fixed 32px minimum gap */
  width: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: var(--z-modal); /* Standardized z-index for modal-level elements */
  /* Allow the row to be flexible but maintain minimum gap */
  min-width: 0; /* Remove rigid min-width to allow proper flex behavior */
}

/* Dashboard header database-row - ensure proper flex behavior within container padding */
.dashboard-header .database-row {
  /* Gap is already set to 32px in parent .database-row rule */
  /* Ensure the row doesn't exceed container bounds and maintains proper spacing */
  max-width: 100%;
  /* When space gets tight, prioritize maintaining the 32px gap */
  flex-wrap: nowrap; /* Prevent wrapping */
  overflow: visible; /* Allow dropdowns to extend beyond container if needed */
}

/* Ensure database-left doesn't grow too much when space is constrained */
.dashboard-header .database-left {
  /* More aggressive max-width to ensure space for database-right */
  max-width: calc(100% - 32px - 200px); /* Leave space for gap + database-right */
}

/* Pure CSS responsive layout - no JavaScript needed */
/* Use CSS custom properties to track available space */
.dashboard-header-content {
  /* CSS custom property to track container width */
  --container-width: 100%;
  /* Calculate if we have enough space for horizontal layout with 32px gap */
  --min-horizontal-width: 972px; /* h1(200px) + gap(40px) + db-left(500px) + min-gap(32px) + db-right(200px) */
}

/* ============================================================================
   UNIFIED PAGE HEADER SYSTEM
   ============================================================================ */

/* Global Page Header - Fixed header for all pages with H1 only */
.page-header {
  position: fixed;
  top: 0;
  left: 276px; /* Updated sidebar width (28px + 220px + 28px = 276px) */
  right: 0;
  background: var(--bg-primary);
  border-bottom: 1.5px solid var(--border-color);
  padding: 28px 28px 28px 28px; /* Match main-content padding */
  z-index: var(--z-header); /* Higher than modals, below overlays */
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* Smooth transition when sidebar collapses */
  min-width: 1024px; /* Consistent with other dashboard cards */
}

/* Adjust page header position when sidebar is collapsed */
.sidebar.collapsed ~ .main-content .page-header {
  left: 116px; /* New collapsed sidebar width: 28px + 60px + 28px = 116px */
}

/* Page header H1 styling */
.page-header h1 {
  font-size: 24px;
  margin: 0;
  margin-top: 10px;
  font-weight: 500;
  color: var(--text-accent);
  flex-shrink: 0;
  transition: margin 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ============================================================================
   DASHBOARD HEADER - Special header with H1 + Database Components
   ============================================================================ */

/* Header Section - Fixed horizontal equivalent of sidebar */
.dashboard-header {
  position: fixed;
  top: 0;
  left: 276px; /* Updated sidebar width (28px + 220px + 28px = 276px) */
  right: 0;
  background: var(--bg-primary);
  border-bottom: 1.5px solid var(--border-color);
  padding: 28px 28px 28px 28px; /* Match main-content padding: top/bottom 40px, left 28px, right 16px */
  z-index: var(--z-header); /* Higher than modals, below overlays */
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* Smooth transition when sidebar collapses */
  min-width: 1024px; /* Consistent with other dashboard cards */
}

/* Adjust header position when sidebar is collapsed */
.sidebar.collapsed ~ .main-content .dashboard-header {
  left: 116px; /* New collapsed sidebar width: 28px + 60px + 28px = 116px */
}

/* Header content wrapper */
.dashboard-header-content {
  display: flex;
  flex-direction: row; /* Default to horizontal layout */
  align-items: flex-start; /* Keep h1 in original position */
  justify-content: space-between;
  gap: 40px;
  width: 100%;
  max-width: none;
  min-width: 1024px; /* Consistent with other dashboard cards */
  /* Smooth transitions for layout changes */
  transition: flex-direction 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              gap 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              align-items 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Header h1 styling */
.dashboard-header h1 {
  font-size: 24px;
  margin: 0;
  margin-top: 10px;
  font-weight: 500;
  color: var(--text-accent);
  flex-shrink: 0; /* Prevent h1 from shrinking */
  /* Smooth transitions for margin changes */
  transition: margin 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dashboard page specific rule: Hide h1 when sidebar is uncollapsed (expanded) */
.sidebar:not(.collapsed) ~ .main-content .dashboard-header h1 {
  display: none;
}

/* Database container in header */
.dashboard-header .database-container {
  flex-shrink: 1; /* Allow database container to shrink if needed */
  min-width: 0 !important; /* Override global min-width rule - allow container to shrink below its content width */
  margin-top: 10px; /* Align with h1's margin-top to match vertical position */
  /* Smooth transitions for width and margin changes */
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              margin 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dashboard content - add top padding to account for fixed header */
.dashboard-content {
  width: 100%;
  /* Dynamic top padding: only dashboard-header height (calculated by JS) */
  padding-top: var(--dashboard-header-height, 0px);
  /* Smooth transition for padding changes */
  transition: padding-top 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Remove the old border styling since we now have a proper header */
.database-row::after {
  display: none;
}
.database-left {
  display: flex;
  align-items: center;
  flex: 1 1 500px; /* Allow growth but maintain 500px minimum */
  min-width: 500px;
  /* Allow more flexible growth - let flex gap handle the spacing */
  gap: 0;
  overflow: hidden; /* Prevent content from overflowing */
}
.database-stat {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 4px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  color: #606F95;
  font-weight: 700;
  min-width: 120px;
}
.database-label {
  font-size: 14px;
  font-weight: 700;
  color: #606F95;
  margin-bottom: 0;
  text-align: left;
}
.database-value-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
  width: 100%;
}
.database-value {
  font-size: 14px;
  font-weight: 700;
  color: #606F95;
  margin-left: 0;
  text-align: left;
}
.database-icon {
  width: 12px;
  height: 12px;
  margin-right: 0;
}
.database-updated-row {
  display: flex;
  align-items: left;
  justify-content: left;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #04AE2C;
  margin-top: 2px;
}
.database-updated-row img {
  width: 12px;
  height: 12px;
  margin-right: 0;
}
.database-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1px;
  height: 35px;
  background: #606F95;
  opacity: 0.1;
  margin: 0 16px;
}
[data-theme="dark"] .database-divider {
  background: #B4B9C5;
  opacity: 0.1;
}
.database-status .database-updated {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
  color: #04AE2C;
  margin-left: 8px;
  gap: 4px;
}
.database-updated img {
  width: 12px;
  height: 12px;
  margin-right: 4px;
}
.database-update-btn {
  width: 100px !important;
  min-width: 100px !important;
  margin-left: 24px;
  background: #04AE2C !important;
  color: #fff !important;
  border-radius: 26px !important;
  font-size: 12px;
  font-weight: 700;
  font-family: 'Amazon Ember', sans-serif;
  height: 32px !important;
  box-shadow: none;
  border: none;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}
.database-update-btn:hover {
  background: rgba(4, 174, 44, 0.8) !important;
}
.database-marketplace-dropdown {
  width: auto;
  min-width: 180px;
  max-width: 220px;
  flex: 0 0 auto;
}
.database-marketplace-dropdown .dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #FFFFFF;
  border: 1.5px solid #E9EBF2;
  border-radius: 8px;
  height: 40px;
  padding: 12px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #606F95;
  cursor: pointer;
  min-width: 0;
  width: auto;
  box-sizing: border-box;
}
.database-marketplace-dropdown .dropdown-header span {
  padding: 0;
  margin: 0;
  white-space: nowrap;
}
.database-marketplace-dropdown .dropdown-header-content {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 0 0 auto;
}
.database-marketplace-dropdown .dropdown-header img[alt="Dropdown"] {
  margin-right: 0;
  margin-left: 8px;
  flex-shrink: 0;
}
.database-marketplace-dropdown .dropdown-header img[alt="All Marketplaces Icon"] {
  margin-right: 0;
  flex-shrink: 0;
}

/* Ensure marketplace dropdown container doesn't create stacking context issues */
.database-marketplace-dropdown {
  position: relative;
  z-index: var(--z-dropdown); /* Standardized z-index for dropdown container */
}

/* Ensure marketplace dropdown header has elevated z-index when dropdown is open */
.database-marketplace-dropdown .dropdown-header {
  position: relative;
  z-index: var(--z-dropdown) !important; /* Standardized z-index for dropdown header */
}

/* Marketplace dropdown menu should have highest priority among dropdowns */
.database-marketplace-dropdown .dropdown-menu {
  z-index: var(--z-modal) !important; /* Standardized z-index for modal-level elements */
  position: absolute !important; /* Ensure proper positioning context */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important; /* Restore visual depth */
}

/* Ensure dropdown header adjusts to content */
.database-container .database-marketplace-dropdown .dropdown-header {
  min-width: 0 !important;
  width: auto !important;
}
/* Responsive header layout - CSS-only solution with 32px gap threshold */

/* Enhanced responsive layout using CSS container queries for precise control */
.dashboard-header-content {
  /* Enable container queries for precise responsive behavior */
  container-type: inline-size;

  /* Default horizontal layout */
  flex-direction: row;
  gap: 40px;
  align-items: flex-start;
}

/* Precise breakpoint calculation for 32px gap requirement:
   - h1 minimum: ~200px
   - database-left minimum: 500px (defined in CSS)
   - database-right minimum: ~200px
   - header gap: 40px (between h1 and database-container)
   - minimum database gap: 32px (between database-left and database-right)
   Total minimum for horizontal: 200 + 40 + 500 + 32 + 200 = 972px
*/

/* Horizontal layout - when container has enough space for 32px gap */
@container (min-width: 972px) {
  .dashboard-header-content {
    flex-direction: row;
    gap: 40px;
    align-items: flex-start;
  }

  .dashboard-header h1 {
    flex-shrink: 0;
    margin-bottom: 0;
  }

  .dashboard-header .database-container {
    flex-shrink: 1;
    margin-top: 8px;
    min-width: 0;
  }

  /* Maintain 32px gap in horizontal layout (already set in parent rule) */
  .dashboard-header .database-row {
    /* Gap is maintained at 32px from parent rule */
    justify-content: space-between; /* Ensure proper spacing distribution */
  }
}

/* Vertical layout - when horizontal would compress gap below 32px */
@container (max-width: 971px) {
  .dashboard-header-content {
    flex-direction: column;
    gap: 24px;
    align-items: flex-start;
  }

  .dashboard-header h1 {
    margin-bottom: 0;
  }

  .dashboard-header .database-container {
    width: 100%;
    margin-top: 0;
  }

  /* In vertical layout, we can reduce gap since elements stack vertically */
  .dashboard-header .database-row {
    gap: 24px; /* Reduced gap for vertical stacking */
    flex-direction: column; /* Ensure vertical stacking */
  }
}
/* Fallback for browsers without container query support */
@supports not (container-type: inline-size) {
  /* Use viewport-based media queries as fallback */
  @media (min-width: 1200px) {
    .dashboard-header-content {
      flex-direction: row;
      gap: 40px;
      align-items: flex-start;
    }

    .dashboard-header h1 {
      flex-shrink: 0;
      margin-bottom: 0;
    }

    .dashboard-header .database-container {
      flex-shrink: 1;
      margin-top: 8px;
      min-width: 0;
    }

    .dashboard-header .database-row {
      /* Gap maintained at 32px from parent rule */
      justify-content: space-between;
    }
  }

  @media (max-width: 1199px) {
    .dashboard-header-content {
      flex-direction: column;
      gap: 24px;
      align-items: flex-start;
    }

    .dashboard-header h1 {
      margin-bottom: 0;
    }

    .dashboard-header .database-container {
      width: 100%;
      margin-top: 0;
    }

    .dashboard-header .database-row {
      gap: 24px; /* Reduced gap for vertical layout */
      flex-direction: column;
    }
  }
}

@media (max-width: 1200px) {
  .database-row {
    flex-wrap: wrap;
    gap: 16px;
  }
  .database-update-btn {
    min-width: 100px;
    width: 100px;
    font-size: 12px;
    height: 32px;
  }
  .database-marketplace-dropdown {
    min-width: 140px;
    width: auto;
  }
  .database-marketplace-dropdown .dropdown-header {
    min-width: 0; /* Fix overflow: allow header to shrink with container */
  }
}
@media (max-width: 800px) {
  .database-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  .database-update-btn, .database-marketplace-dropdown {
    margin-left: 0;
  }
}

.database-label-row {
  display: flex;
  align-items: center;
  gap: 8px;
  text-align: left;
}

.listing-status-top,
.database-value-row,
.database-updated-row,
.database-label-row {
  display: flex;
  align-items: center;
}

.database-label-row .database-label {
  margin-top: 2px;
}

.database-value-row > .database-value,
.database-updated-row > .database-updated-text,
.database-label-row > .database-label,
.listing-status-top > .listing-status-number {
  margin-top: 2px;
}

.flex-row {
  display: flex;
  align-items: center;
}
.flex-row > img + span,
.flex-row > img + div,
.flex-row > svg + span,
.flex-row > svg + div {
  margin-top: 2px;
}

/* Update existing row classes to use flex-row styles directly */
.database-value-row,
.database-updated-row,
.database-label-row,
.listing-status-top {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.designs-stat, .products-stat {
  width: fit-content;
  min-width: 0;
}

/* Time Tracker Styles */
.time-tracker-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.time-tracker-block {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  min-width: 80px;
}

.time-tracker-label {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 11px;
  font-weight: 500;
  color: #606F95;
  line-height: 1;
  margin-bottom: 2px;
}

.time-tracker-content {
  display: flex;
  align-items: center;
  gap: 6px;
}

.time-tracker-icon {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

.time-tracker-time {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #232F3E;
  line-height: 1;
  letter-spacing: 0.5px;
}

/* Divider between time trackers */
.time-tracker-divider {
  width: 1px;
  height: 32px;
  background: #E9EBF2;
  flex-shrink: 0;
}

/* Divider between time tracker and database */
.database-time-divider {
  width: 1px;
  height: 40px;
  background: #E9EBF2;
  margin: 0 16px;
  flex-shrink: 0;
}

/* Dark theme support for time tracker */
[data-theme="dark"] .time-tracker-label {
  color: #B4B9C5;
}

[data-theme="dark"] .time-tracker-time {
  color: #FFFFFF;
}

[data-theme="dark"] .time-tracker-divider,
[data-theme="dark"] .database-time-divider {
  background: #3A3D4A;
}

.database-updated-text {
  font-size: 10px;
}

[data-theme="dark"] .database-marketplace-dropdown .dropdown-header,
[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu {
  background: var(--bg-primary);
  border: 1.5px solid var(--border-color) !important;
  border-radius: 8px;
  color: var(--text-primary);
}
[data-theme="dark"] .database-marketplace-dropdown .dropdown-header span,
[data-theme="dark"] .database-marketplace-dropdown .dropdown-item span {
  color: var(--text-primary) !important;
}

.designs-value, .products-value {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  line-height: 1.2;
  margin-top: 2px;
}
[data-theme="dark"] .designs-value,
[data-theme="dark"] .products-value {
  color: #B4B9C5;
}

.hidden {
  display: none !important;
}

[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu .dropdown-item:hover,
[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu .dropdown-item.selected {
  background: #292E38 !important;
  color: var(--text-accent) !important;
}

.snap-dropdown .dropdown-item:first-child:hover {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
.snap-dropdown .dropdown-item:last-child:hover {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu .dropdown-item:first-child:hover,
[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu .dropdown-item:first-child.selected {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu .dropdown-item:last-child:hover,
[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu .dropdown-item:last-child.selected {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

/* --- Ad Spend Section Styles (Figma-accurate, matches screenshot) --- */
.ad-spend {
  width: 100%;
  background: var(--bg-primary);
  border-radius: 14px;
  padding: 24px !important;
  margin-top: 16px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 18px;
  position: relative !important;
}
.ad-spend-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.ad-spend-header-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}
.ad-spend-header-center {
  display: flex;
  align-items: center;
}
.ad-spend-header-right {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 8px;
}
.ad-spend-ads-pill {
  background: #E9EBF2;
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 15px;
  border-radius: 6px;
  padding: 2px 14px 2px 14px;
  letter-spacing: 0.02em;
  display: inline-block;
}
.ad-spend-today-label {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 500;
  position: relative;
  top: 1px;
}
.ad-spend-header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
}
.ad-spend-header-label {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
}
.ad-spend-header-value {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
}
.ad-spend-nav-btn {
  background: none !important;
  border: none !important;
  width: auto !important;
  height: auto !important;
  box-shadow: none !important;
  padding: 0 !important;
  min-width: 0 !important;
  min-height: 0 !important;
  max-width: none !important;
  max-height: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ad-spend-nav-btn img {
  width: 16px !important;
  height: 16px !important;
}
.ad-spend-marketplaces-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 0;
}
.ad-spend-marketplace-col,
.ad-spend-marketplace-col-us,
.ad-spend-marketplace-col-uk,
.ad-spend-marketplace-col-de,
.ad-spend-marketplace-col-fr,
.ad-spend-marketplace-col-it,
.ad-spend-marketplace-col-es,
.ad-spend-marketplace-col-jp {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: auto;
}
.ad-spend-flag {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-bottom: 6px;
  box-shadow: 0 1px 2px rgba(96, 111, 149, 0.08);
}
.ad-spend-value.ad-spend-currency {
  font-size: 14px;
  font-weight: 500;
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  margin-bottom: 2px;
  letter-spacing: 0.01em;
  text-align: center;
}
.ad-spend-orders {
  font-size: 10px;
  font-weight: 500;
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
}
.ad-spend-acos-pill, .ad-spend-acos-green, .ad-spend-acos-red, .ad-spend-ads-pill {
  background: none !important;
  border: none !important;
  color: inherit !important;
  padding: 0 !important;
  margin: 0 2px 0 2px !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  display: inline-flex !important;
  align-items: center !important;
}
.ad-spend-acos-green {
  background: #04AE2C;
  color: #fff;
}
.ad-spend-acos-red {
  background: #FF391F;
  color: #fff;
}
.ad-spend-acos-value {
  font-size: 8px;
  font-weight: 400;
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
}
.ad-spend-marketplace-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: center;
  width: 1px;
  height: 35px;
  background: #606F95;
  opacity: 0.1;
  margin: 0 8px;
}
/* Hide divider for last column */
.ad-spend-marketplace-col:last-of-type + .ad-spend-marketplace-divider,
.ad-spend-marketplace-col-jp + .ad-spend-marketplace-divider {
  display: none;
}
/* Responsive adjustments */
@media (max-width: 1200px) {
  .ad-spend-marketplaces-row {
    flex-wrap: wrap;
  }
  .ad-spend-marketplace-col,
  .ad-spend-marketplace-col-us,
  .ad-spend-marketplace-col-uk,
  .ad-spend-marketplace-col-de,
  .ad-spend-marketplace-col-fr,
  .ad-spend-marketplace-col-it,
  .ad-spend-marketplace-col-es,
  .ad-spend-marketplace-col-jp {
    min-width: 0;
    width: auto;
    padding: 0;
  }
}
@media (max-width: 900px) {
  .ad-spend-header-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  .ad-spend-header-center {
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 6px;
  }
  .ad-spend-marketplaces-row {
    flex-direction: column;
    gap: 12px;
  }
  .ad-spend-marketplace-divider {
    display: none;
  }
  .ad-spend-marketplace-col,
  .ad-spend-marketplace-col-us,
  .ad-spend-marketplace-col-uk,
  .ad-spend-marketplace-col-de,
  .ad-spend-marketplace-col-fr,
  .ad-spend-marketplace-col-it,
  .ad-spend-marketplace-col-es,
  .ad-spend-marketplace-col-jp {
    min-width: 0;
    width: auto;
    padding: 0;
  }
}
@media (max-width: 600px) {
  .ad-spend {
    padding: 10px 4px 8px 4px;
  }
  .ad-spend-header-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }
  .ad-spend-header-center {
    flex-wrap: wrap;
    gap: 4px;
  }
  .ad-spend-marketplaces-row {
    flex-direction: column;
    gap: 8px;
  }
  .ad-spend-marketplace-col,
  .ad-spend-marketplace-col-us,
  .ad-spend-marketplace-col-uk,
  .ad-spend-marketplace-col-de,
  .ad-spend-marketplace-col-fr,
  .ad-spend-marketplace-col-it,
  .ad-spend-marketplace-col-es,
  .ad-spend-marketplace-col-jp {
    min-width: 0;
    width: 100%;
    padding: 0;
  }
}
[data-theme="dark"] .ad-spend {
  background: var(--bg-primary);
}
[data-theme="dark"] .ad-spend-header-row,
[data-theme="dark"] .ad-spend-header-center,
[data-theme="dark"] .ad-spend-header-label,
[data-theme="dark"] .ad-spend-header-value,
[data-theme="dark"] .ad-spend-orders,
[data-theme="dark"] .ad-spend-acos-value {
  color: #B4B9C5;
}
[data-theme="dark"] .ad-spend-ads-pill {
  background: #292E38;
  color: #B4B9C5;
}
[data-theme="dark"] .ad-spend-marketplace-divider {
  background: #B4B9C5;
  opacity: 0.1;
}


[data-theme="dark"] .ad-spend-value {
  color: #fff;
}
[data-theme="dark"] .ad-spend-acos-green {
  background: #04AE2C;
  color: #fff;
}
[data-theme="dark"] .ad-spend-acos-red {
  background: #FF391F;
  color: #fff;
}
/* --- End Ad Spend Section Styles --- */

.ad-spend-acos-pill {
  width: 24px !important;
  height: 12px !important;
  min-width: 24px !important;
  min-height: 12px !important;
  max-width: 24px !important;
  max-height: 12px !important;
  object-fit: contain;
  display: inline-block !important;
}
.ad-spend-orders-row {
  display: flex;
  align-items: center;
  gap: 4px;
}

[data-theme="dark"] .ad-spend-today-label {
  color: #fff !important;
}

.ad-spend-header-center {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
}
.ad-spend-header-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1px;
  height: 17.5px;
  background: #606F95;
  opacity: 0.1;
  margin: 0 8px;
}
[data-theme="dark"] .ad-spend-header-divider {
  background: #B4B9C5;
  opacity: 0.1;
}

.ad-spend-header-metric-group {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  width: fit-content;
}



.ad-spend-nav-btn.ad-spend-prev:hover,
.ad-spend-nav-btn.ad-spend-next:hover {
  opacity: 0.5;
}

.designs-label, .products-label {
  font-size: 14px;
  font-weight: 500;
  color: #606F95;
  margin-bottom: 0;
  text-align: left;
}

/* Add pointer cursor for ad spend nav buttons on hover */
.ad-spend-nav-btn:hover {
  cursor: pointer;
}

/* Disabled state for ad spend navigation buttons */
.ad-spend-nav-btn[style*="pointer-events: none"],
.ad-spend-nav-btn[aria-disabled="true"] {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.ad-spend-nav-btn[style*="pointer-events: none"]:hover,
.ad-spend-nav-btn[aria-disabled="true"]:hover {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Ensure disabled buttons don't show hover effects */
.ad-spend-nav-btn[style*="pointer-events: none"] img,
.ad-spend-nav-btn[aria-disabled="true"] img {
  filter: grayscale(100%) !important;
  transition: filter 0.2s ease;
}

.ad-spend-header-right-group {
  display: flex;
  align-items: center;
  gap: 32px;
}

[data-theme="dark"] .database-label {
  color: #FFFFFF !important;
}

/* Prevent containers shrinking below 1024px */
.dashboard-component,
.account-status,
.listings-status,
.listings-status-overview,
.ad-spend,
.database-container,
.sales-cards-container,
.last-week-sales-card,
.today-vs-previous-years-card {
  min-width: 1024px !important;
}

/* Fix listing overview and ad-spend to maintain desktop layout at small widths */
@media (max-width: 1023px) {
  .listings-status-overview,
  .ad-spend,
  .last-week-sales-card {
    min-width: 1024px !important;
    width: auto !important;
  }
  /* Listing Overview Layout */
  .listings-status-row {
    flex-wrap: nowrap !important;
    flex-direction: row !important;
  }
  .listing-status-card {
    flex: 0 0 auto !important;
    width: auto !important;
    min-width: auto !important;
  }
  .listing-status-divider {
    display: flex !important;
  }
  /* Ad Spend Layout */
  .ad-spend-header-row {
    flex-direction: row !important;
  }
  .ad-spend-marketplaces-row {
    flex-wrap: nowrap !important;
  }
}

/* Enforce desktop layout for Listings Overview, Ad Spend, and Last Week Sales */
.listings-status-overview,
.ad-spend,
.last-week-sales-card,
.monthly-sales-card,
.yearly-sales-card {
  min-width: 1024px !important;
  width: auto !important;
  padding: 24px !important;
}
.listings-status-row,
.ad-spend-header-row,
.ad-spend-marketplaces-row {
  flex-wrap: nowrap !important;
  flex-direction: row !important;
}

/* Global fixed-width enforcement: any top-level container inside main content respects 1024px rule */
.main-content > * {
  min-width: 1024px !important;
}

/* Exception: Allow dropdown containers to size naturally */
.main-content .marketplace-focus-dropdown-container {
  min-width: unset !important;
}

/* Exception: Ensure four sales cards individual cards are not affected by global min-width */
.main-content .four-sales-cards-section .current-month-card-div,
.main-content .four-sales-cards-section .last-month-card-div,
.main-content .four-sales-cards-section .current-year-card-div,
.main-content .four-sales-cards-section .last-year-card-div {
  min-width: 0 !important;
}

/* Exception: Ensure top four sales cards individual cards are not affected by global min-width */
.main-content .top-four-sales-cards-section .top-day-card-div,
.main-content .top-four-sales-cards-section .top-week-card-div,
.main-content .top-four-sales-cards-section .top-month-card-div,
.main-content .top-four-sales-cards-section .top-year-card-div {
  min-width: 0 !important;
}

/* Exception: Ensure payout cards individual cards are not affected by global min-width */
.main-content .payout-section .next-payout-card-div,
.main-content .payout-section .previous-payout-card-div {
  min-width: 0 !important;
}

/* Prevent database container from wrapping its content on narrow viewports */
.database-container .database-row {
  flex-wrap: nowrap !important;
  flex-direction: row !important;
}
/* Keep marketplace dropdown auto width */
.database-container .database-marketplace-dropdown {
  min-width: 170px !important;
  width: auto !important;
}

/* Sales Cards Container */
.sales-cards-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
  min-width: 1024px;
  align-items: flex-start; /* Allow cards to have different heights */
}

/* First row container for Today's and Yesterday's cards */
.sales-cards-row {
  width: 100%;
  display: flex;
  gap: 16px;
  align-items: stretch; /* Ensure row children match height */
}

/* Today's and Yesterday's cards - side by side */
.sales-cards-container > .todays-sales-card-div,
.sales-cards-container > .yesterdays-sales-card-div {
  flex: 1;
  width: calc(50% - 8px);
}
/* Sales Card Styles (Figma accurate) */
.todays-sales-card-div,
.yesterdays-sales-card-div,
.current-month-card-div,
.last-month-card-div,
.current-year-card-div,
.last-year-card-div,
.top-day-card-div,
.top-week-card-div,
.top-month-card-div,
.top-year-card-div {
  flex: 1;
  background: var(--bg-primary);
  border-radius: 14px;
  padding: 24px; /* Equal padding all around - scrollbar is overlay and doesn't affect layout */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: 880px; /* Fixed height */
  min-height: 880px; /* Fixed minimum height */
  max-height: 880px; /* Fixed maximum height */
  overflow: hidden; /* Remove scrolling from main card */
}
.sales-cards-row > .todays-sales-card-div,
.sales-cards-row > .yesterdays-sales-card-div,
.sales-cards-row > .current-month-card-div,
.sales-cards-row > .last-month-card-div,
.sales-cards-row > .current-year-card-div,
.sales-cards-row > .last-year-card-div,
.sales-cards-row > .top-day-card-div,
.sales-cards-row > .top-week-card-div,
.sales-cards-row > .top-month-card-div,
.sales-cards-row > .top-year-card-div {
  align-self: stretch; /* Override to enforce equal heights per row */
}

/* Override: For Today and Yesterday cards, use dynamic min-height and fixed max-height only */
.todays-sales-card-div,
.yesterdays-sales-card-div {
  height: auto; /* allow content to define height up to max */
  min-height: 0; /* will be set via JS to at least one listing height */
  max-height: 880px; /* cap */
}

/* Dark theme styles for all sales cards including top four sales cards */
[data-theme="dark"] .todays-sales-card-div,
[data-theme="dark"] .yesterdays-sales-card-div,
[data-theme="dark"] .current-month-card-div,
[data-theme="dark"] .last-month-card-div,
[data-theme="dark"] .current-year-card-div,
[data-theme="dark"] .last-year-card-div,
[data-theme="dark"] .top-day-card-div,
[data-theme="dark"] .top-week-card-div,
[data-theme="dark"] .top-month-card-div,
[data-theme="dark"] .top-year-card-div {
  background: var(--bg-primary);
  border-color: #2A2D35;
}

/* Dynamic padding for Today's and Yesterday's sales cards based on scrollbar visibility */
/* Total right space always = 24px, distributed as: content gap + scrollbar width + card padding */

.sales-cards-container .sales-cards-row .todays-sales-card-div.scrollbar-visible,
.sales-cards-container .sales-cards-row .yesterdays-sales-card-div.scrollbar-visible {
  padding-right: 10px; /* 8px content gap + 6px scrollbar + 10px card padding = 24px total */
}

.sales-cards-container .sales-cards-row .todays-sales-card-div.scrollbar-hidden,
.sales-cards-container .sales-cards-row .yesterdays-sales-card-div.scrollbar-hidden {
  padding-right: 24px; /* Full 24px when no scrollbar */
}

/* Conditional scrollable content padding - only when scrollbar is visible */
.sales-cards-container .sales-cards-row .todays-sales-card-div.scrollbar-visible .sales-scrollable-content,
.sales-cards-container .sales-cards-row .yesterdays-sales-card-div.scrollbar-visible .sales-scrollable-content {
  padding-right: 8px; /* Creates 8px gap between content and scrollbar when scrollbar is visible */
}

.sales-cards-container .sales-cards-row .todays-sales-card-div.scrollbar-hidden .sales-scrollable-content,
.sales-cards-container .sales-cards-row .yesterdays-sales-card-div.scrollbar-hidden .sales-scrollable-content {
  padding-right: 0; /* No right padding when scrollbar is hidden - use full width */
}

/* Sales Scrollable Content - Contains marketplaces, search, and listings */
.sales-scrollable-content {
  flex: 1; /* Take remaining space after fixed elements */
  overflow-y: auto; /* Enable vertical scrolling */
  overflow-x: hidden; /* Completely forbid horizontal scrolling */
  display: flex;
  flex-direction: column;
  min-height: 0; /* Allow flex shrinking */
  margin-top: -24px; /* Pull scrollable area up to start directly under divider */
  margin-bottom: -24px; /* Extend scrollable area to bottom edge of card */
  padding-top: 24px; /* Restore spacing for content within scrollable area */
  padding-bottom: 24px; /* Restore spacing for content at bottom */
  padding-right: 0; /* No right padding by default - full width when no scrollbar */
}

/* Modern scrollbar - overlay style to prevent width changes */
.sales-scrollable-content::-webkit-scrollbar {
  width: 6px;
  background: transparent;
  position: absolute;
  z-index: var(--z-dropdown);
}



/* Hide horizontal scrollbar completely */
.sales-scrollable-content::-webkit-scrollbar:horizontal {
  display: none;
  height: 0px;
}

/* Modern vertical scrollbar - always reserve space but show handle on hover */
.sales-scrollable-content::-webkit-scrollbar:vertical {
  width: 6px;
}

/* Ensure horizontal scrollbar stays hidden */
.sales-scrollable-content:hover::-webkit-scrollbar:horizontal,
.sales-scrollable-content:focus-within::-webkit-scrollbar:horizontal {
  display: none;
  height: 0px;
}

/* Hide scrollbar track completely for modern look */
.sales-scrollable-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 0;
  margin: 0;
  margin-left: 6px; /* Add 6px left padding to the scrollbar */
}

/* Modern scrollbar handle - light theme */
.sales-scrollable-content::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 3px;
  border: none;
  transition: background 0.2s ease;
}

.sales-scrollable-content:hover::-webkit-scrollbar-thumb,
.sales-scrollable-content:focus-within::-webkit-scrollbar-thumb {
  background: #D9DDEB;
}

.sales-scrollable-content::-webkit-scrollbar-thumb:hover {
  background: #E9EBF2;
  border-radius: 12px;
}

/* Firefox modern scrollbar - true overlay behavior */
.sales-scrollable-content {
  scrollbar-width: none; /* Hide scrollbar completely by default */
  scrollbar-color: transparent transparent; /* Hide by default */
  overflow-x: hidden !important; /* Completely forbid horizontal scrollbar */
  overflow-y: auto; /* Allow vertical scrolling */
}

/* Firefox: Show scrollbar on hover but keep it overlay */
.sales-scrollable-content:hover {
  scrollbar-width: thin; /* Show thin scrollbar on hover */
  scrollbar-color: #D9DDEB transparent; /* Show on hover */
}

/* Firefox: Prevent layout shift by using overlay positioning */
@supports (scrollbar-width: thin) {
  .sales-scrollable-content {
    /* Create a wrapper effect to contain the scrollbar overlay */
    position: relative;
    overflow-y: scroll; /* Force scrollbar space reservation */
    scrollbar-width: thin; /* Always reserve space */
    scrollbar-color: transparent transparent; /* But keep it invisible */
  }

  .sales-scrollable-content:hover {
    scrollbar-color: #D9DDEB transparent; /* Show on hover */
  }
}

[data-theme="dark"] .todays-sales-card-div,
[data-theme="dark"] .yesterdays-sales-card-div,
[data-theme="dark"] .current-month-card-div,
[data-theme="dark"] .last-month-card-div,
[data-theme="dark"] .current-year-card-div,
[data-theme="dark"] .last-year-card-div,
[data-theme="dark"] .top-day-card-div,
[data-theme="dark"] .top-week-card-div,
[data-theme="dark"] .top-month-card-div,
[data-theme="dark"] .top-year-card-div {
  background: var(--bg-primary);
}

/* Dark theme modern scrollbar */
[data-theme="dark"] .sales-scrollable-content::-webkit-scrollbar-thumb {
  background: transparent;
}

[data-theme="dark"] .sales-scrollable-content:hover::-webkit-scrollbar-thumb,
[data-theme="dark"] .sales-scrollable-content:focus-within::-webkit-scrollbar-thumb {
  background: #2F3341;
}

[data-theme="dark"] .sales-scrollable-content::-webkit-scrollbar-thumb:hover {
  background: #3F455B;
}

/* Dark theme scrollbar track - maintain 6px left padding */
[data-theme="dark"] .sales-scrollable-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 0;
  margin: 0;
  margin-left: 6px; /* Add 6px left padding to the scrollbar */
}

/* Firefox dark theme */
[data-theme="dark"] .sales-scrollable-content {
  scrollbar-color: transparent transparent; /* Hide by default */
}

[data-theme="dark"] .sales-scrollable-content:hover {
  scrollbar-color: #2F3341 transparent; /* Show on hover */
}

/* Dark theme Firefox overlay support */
@supports (scrollbar-width: thin) {
  [data-theme="dark"] .sales-scrollable-content {
    scrollbar-color: transparent transparent; /* Keep invisible by default */
  }

  [data-theme="dark"] .sales-scrollable-content:hover {
    scrollbar-color: #2F3341 transparent; /* Show on hover */
  }
}

/* Modern scrollbar for main page - both vertical and horizontal allowed */
body::-webkit-scrollbar {
  width: 0px;
  height: 0px;
  background: transparent;
  transition: width 0.2s ease, height 0.2s ease;
}

/* Show modern scrollbar on hover/scroll */
body:hover::-webkit-scrollbar:vertical,
body:focus-within::-webkit-scrollbar:vertical {
  width: 6px;
}

body:hover::-webkit-scrollbar:horizontal,
body:focus-within::-webkit-scrollbar:horizontal {
  height: 6px;
}

/* Modern scrollbar track - invisible */
body::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

/* Modern scrollbar thumb */
body::-webkit-scrollbar-thumb {
  background: #D9DDEB;
  border-radius: 3px;
  transition: background 0.2s ease;
}

body:hover::-webkit-scrollbar-thumb,
body:focus-within::-webkit-scrollbar-thumb {
  background: #D9DDEB;
}

body::-webkit-scrollbar-thumb:hover {
  background: #E9EBF2;
}

/* Firefox modern scrollbar for main page */
body {
  scrollbar-width: none; /* Hide by default */
  scrollbar-color: transparent transparent;
}

body:hover {
  scrollbar-width: thin;
  scrollbar-color: #D9DDEB transparent;
}

/* Dark mode scrollbar for main page */
[data-theme="dark"] body::-webkit-scrollbar-thumb {
  background: #2F3341;
}

[data-theme="dark"] body::-webkit-scrollbar-thumb:hover {
  background: #3F455B;
}

[data-theme="dark"] body:hover {
  scrollbar-color: #2F3341 transparent;
}
.Sales-title-date-div {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  align-self: stretch;
  margin-bottom: 8px;
}
.reviews-title-date-div {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  align-self: stretch;
  margin-bottom: 8px;
}
.sales-card-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
}
.reviews-card-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
}
.sales-card-title,
.reviews-card-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: #606F95;
}
[data-theme="dark"] .sales-card-title,
[data-theme="dark"] .reviews-card-title {
  color: #B4B9C5;
}
.sales-card-date {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #606F95;
  /* No extra margin */
}
[data-theme="dark"] .sales-card-date {
  color: #B4B9C5;
}

/* Sales Analytics Div (Figma accurate) */
.sales-analytics-div,
.reviews-analytics-div {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  margin-top: 4px;
  gap: 0;
  padding-right: 14px;
}
.sales-count-div,
.reviews-count-div {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 32px;
}

/* Updated sales analytics layout for the 4 new cards */
.four-sales-cards-section .sales-analytics-div {
  display: flex;
  flex-direction: column; /* Stack elements vertically */
  width: 100%;
  gap: 16px; /* Gap between rows */
}

/* Top row: sales count and comparison container side by side */
.four-sales-cards-section .sales-analytics-div .top-row {
  display: flex;
  align-items: center; /* Center align to properly align comparison with sales count */
  gap: 16px; /* 16px gap between sales count and comparison container as requested */
  width: 100%; /* Ensure top-row takes full width of parent card */
  box-sizing: border-box; /* Include padding and border in width calculation */
  max-width: 100%; /* Prevent overflow beyond parent */
  overflow: hidden; /* Prevent any child elements from overflowing */
}

.four-sales-cards-section .sales-count-div {
  padding-right: 0;
  margin-right: 0;
  flex-shrink: 0; /* Don't shrink the sales count */
  max-width: calc(50% - 8px); /* Limit sales count to half the width minus gap */
}

/* Ensure analytics-div is positioned below both sales count and comparison-container */
.four-sales-cards-section .analytics-div {
  width: 100% !important; /* Override fixed width */
  margin-top: 0 !important; /* Remove margin as we're using gap in parent */
}

/* Updated sales analytics layout for the top 4 new cards (without comparison container) */
.top-four-sales-cards-section .sales-analytics-div {
  display: flex;
  flex-direction: column; /* Stack elements vertically */
  width: 100%;
  gap: 16px; /* Gap between rows */
}

/* Top row: sales count only (no comparison container) */
.top-four-sales-cards-section .sales-analytics-div .top-row {
  display: flex;
  align-items: center;
  width: 100%; /* Ensure top-row takes full width of parent card */
  box-sizing: border-box; /* Include padding and border in width calculation */
  max-width: 100%; /* Prevent overflow beyond parent */
  overflow: hidden; /* Prevent any child elements from overflowing */
}

.top-four-sales-cards-section .sales-count-div {
  padding-right: 32px; /* Match original sales-count-div padding */
  margin-right: 24px; /* Match original sales-count-div margin */
  flex-shrink: 0; /* Don't shrink the sales count */
  display: flex;
  align-items: center;
  justify-content: center; /* Center the sales count like original cards */
}

/* Ensure analytics-div is positioned below sales count */
.top-four-sales-cards-section .analytics-div {
  width: 100% !important; /* Override fixed width */
  margin-top: 0 !important; /* Remove margin as we're using gap in parent */
}

/* Four Sales Cards Section */
.main-content .four-sales-cards-section {
  width: 100%;
  margin-top: 16px; /* Add 16px top margin as requested */
  min-width: 1024px !important; /* Same as sales-cards-container, override global rule */
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start; /* Allow cards to have different heights */
}

/* Top Four Sales Cards Section */
.main-content .top-four-sales-cards-section {
  width: 100%;
  margin-top: 16px; /* Add 16px top margin as requested */
  min-width: 1024px !important; /* Same as sales-cards-container, override global rule */
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start; /* Allow cards to have different heights */
}

/* Update sales-cards-row for four-sales-cards-section specifically */
.four-sales-cards-section .sales-cards-row {
  display: flex;
  gap: 16px;
  margin-bottom: 0; /* Remove bottom margin from sales-cards-row elements as requested */
  width: 100%; /* Use full width to fill parent container */
}

/* Update sales-cards-row for top-four-sales-cards-section specifically */
.top-four-sales-cards-section .sales-cards-row {
  display: flex;
  gap: 16px;
  margin-bottom: 0; /* Remove bottom margin from sales-cards-row elements as requested */
  width: 100%; /* Use full width to fill parent container */
}

.sales-cards-row .todays-sales-card-div,
.sales-cards-row .yesterdays-sales-card-div,
.sales-cards-row .current-month-card-div,
.sales-cards-row .last-month-card-div,
.sales-cards-row .current-year-card-div,
.sales-cards-row .last-year-card-div,
.sales-cards-row .top-day-card-div,
.sales-cards-row .top-week-card-div,
.sales-cards-row .top-month-card-div,
.sales-cards-row .top-year-card-div {
  flex: 1;
  width: calc(50% - 8px);
}

/* Ensure four sales cards behave exactly like Today's/Yesterday's cards */
.four-sales-cards-section .sales-cards-row .current-month-card-div,
.four-sales-cards-section .sales-cards-row .last-month-card-div,
.four-sales-cards-section .sales-cards-row .current-year-card-div,
.four-sales-cards-section .sales-cards-row .last-year-card-div {
  flex: 1;
  width: calc(50% - 8px);
  min-width: 0 !important; /* Override global min-width to prevent horizontal scrolling */
}

/* Ensure top four sales cards behave exactly like Today's/Yesterday's cards */
.top-four-sales-cards-section .sales-cards-row .top-day-card-div,
.top-four-sales-cards-section .sales-cards-row .top-week-card-div,
.top-four-sales-cards-section .sales-cards-row .top-month-card-div,
.top-four-sales-cards-section .sales-cards-row .top-year-card-div {
  flex: 1;
  width: calc(50% - 8px);
  min-width: 0 !important; /* Override global min-width to prevent horizontal scrolling */
}

/* Comparison Container */
.comparison-container {
  display: flex;
  flex-direction: column;
}

.four-sales-cards-section .comparison-container {
  margin-top: 0; /* Remove margin as we're using gap in parent */
  flex-shrink: 1; /* Allow comparison container to shrink if needed */
  min-width: 0; /* Allow text to wrap/truncate if necessary */
  max-width: calc(50% - 8px); /* Limit to 50% minus gap to prevent overflow */
  overflow: hidden; /* Hide overflow */
  width: auto; /* Let content determine width up to max-width */
}

.comparison-content {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0; /* Don't shrink the percentage/arrow content */
  min-width: 0; /* Allow content to be flexible */
}

.comparison-arrow {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

.comparison-percentage {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
}

.comparison-percentage.positive {
  color: #04AE2C;
}

.comparison-percentage.negative {
  color: #FF391F;
}

.comparison-label {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  color: #687078;
}

/* Ensure comparison label doesn't overflow in four sales cards */
.four-sales-cards-section .comparison-label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  flex-shrink: 1; /* Allow label to shrink */
  min-width: 0; /* Allow text to be truncated */
}

/* Remove hover states from marketplace columns in new cards */
.four-sales-cards-section .marketplace-col:hover {
  background-color: transparent !important;
  cursor: default !important;
  transform: none !important;
}

.four-sales-cards-section .marketplace-col {
  pointer-events: none;
}

/* Remove hover states from marketplace columns in top new cards */
.top-four-sales-cards-section .marketplace-col:hover {
  background-color: transparent !important;
  cursor: default !important;
  transform: none !important;
}

.top-four-sales-cards-section .marketplace-col {
  pointer-events: none;
}

/* Insights and Feedback Section */
.main-content .insights-and-feedback-section {
  width: 100%;
  min-width: 1024px !important;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
}

.insights-and-feedback-section .sales-cards-row {
  display: grid; /* Ensure exact equal heights for both cards */
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 16px;
  width: 100%;
  align-items: stretch;
  margin-top: 16px; /* requested spacing above lifetime insights & reviews row */
}

.insights-and-feedback-section .lifetime-insights-card-div,
.insights-and-feedback-section .customer-reviews-card-div {
  flex: 1;
  width: 100%; /* grid handles equal width */
  min-width: 0 !important;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary); /* Card background */
  border-radius: 14px; /* Match sales cards */
  padding: 24px; /* Consistent inner spacing */
  box-sizing: border-box;
  overflow: hidden; /* Prevent overflow like other cards */
}
/* Adjust right padding only for reviews card */
.insights-and-feedback-section .customer-reviews-card-div { padding-right: 16px !important; }

/* Equalize card heights */
.insights-and-feedback-section .lifetime-insights-card-div,
.insights-and-feedback-section .customer-reviews-card-div {
  height: 100%;
}

/* Add spacing above the analytics section within Lifetime Insights card */
.insights-and-feedback-section .lifetime-insights-card-div .analytics-div {
  margin-top: 16px;
}

/* Dark theme for insights and feedback cards */
[data-theme="dark"] .insights-and-feedback-section .lifetime-insights-card-div,
[data-theme="dark"] .insights-and-feedback-section .customer-reviews-card-div {
  background: var(--bg-primary);
  border-color: #2A2D35;
}

/* Ensure children don't overflow card width */
.insights-and-feedback-section .lifetime-insights-card-div > *,
.insights-and-feedback-section .customer-reviews-card-div > * {
  width: 100% !important;
  min-width: 0 !important;
}

/* Divider alignment inside these cards (match sales cards) */
.insights-and-feedback-section .lifetime-insights-card-div > .sales-section-divider,
.insights-and-feedback-section .customer-reviews-card-div > .sales-section-divider,
.insights-and-feedback-section .customer-reviews-card-div > .reviews-section-divider {
  margin-left: -24px !important;
  margin-right: -24px !important;
}
/* Reviews card has smaller right padding; adjust divider to touch edge */
.insights-and-feedback-section .customer-reviews-card-div > .sales-section-divider,
.insights-and-feedback-section .customer-reviews-card-div > .reviews-section-divider { margin-right: -16px !important; }

/* Fix scroll cutoff - reviews-list should scroll under divider line and extend to card edge */
.customer-reviews-card-div .reviews-list {
  margin-top: -24px; /* Ignore divider's bottom margin - start scrolling right under the divider line */
  padding-top: 24px; /* Restore visual spacing */
  margin-bottom: -24px; /* Ignore card's bottom padding - extend to card edge */
  padding-bottom: 24px; /* Restore visual spacing */
}

/* Prevent empty reviews list from taking space when no reviews are visible */
.customer-reviews-card-div .reviews-list:empty {
  display: none !important;
}

/* Disable hover states like other new sections */
.insights-and-feedback-section .marketplace-col:hover {
  background-color: transparent !important;
  cursor: default !important;
  transform: none !important;
}
.insights-and-feedback-section .marketplace-col { pointer-events: none; }

/* Lifetime data grid */
.lifetime-data-div {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr)); /* 3 cols x 2 rows per Figma */
  gap: 24px 16px;
  width: 100%; /* ensure full card width */
}
.lifetime-data-item { display: flex; flex-direction: column; gap: 6px; }
.lifetime-data-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #606F95;
}
/* Payout Section */
.main-content .payout-section {
  width: 100%;
  min-width: 1024px !important;
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
  align-items: flex-start;
}

.payout-cards-row {
  display: flex;
  gap: 16px;
  width: 100%;
}

.next-payout-card-div,
.previous-payout-card-div {
  flex: 1;
  background: var(--bg-primary);
  border-radius: 14px;
  padding: 24px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 18px;
  min-width: 540px;
}

.payout-title-date-div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.payout-card-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: var(--text-primary);
}

.payout-date {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #606F95;
}

/* Payout data grid (same layout as lifetime-data-div) */
.payout-data-div {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 24px 16px;
  width: 100%;
}

.payout-data-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.payout-data-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #606F95;
}

.after-taxes-per-value {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #606F95;
}

.payout-data-value {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  color: var(--text-primary);
}

/* Dark theme support for payout data labels */
[data-theme="dark"] .payout-data-label {
  color: #FFFFFF;
}

[data-theme="dark"] .after-taxes-per-value {
  color: #FFFFFF;
}

[data-theme="dark"] .payout-data-value {
  color: var(--text-primary);
}

/* Marketplace payout row */
.marketplaces-payout-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0px;
  width: 100%;
}

.marketplaces-payout-row .marketplace-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px 8px;
  border-radius: 8px;
  transition: opacity 0.2s ease;
  cursor: pointer;
}

.marketplaces-payout-row .marketplace-col.active {
  opacity: 1;
}

/* Removed payout-specific hover styles - now using global marketplace styles */

/* In payout cards only, remove left/right padding from marketplace columns */
.payout-cards-row .marketplace-col {
  padding-left: 3px;
  padding-right: 3px;
}

/* Keep marketplaces-payout-row visible; per-marketplace logic hides non-selected columns via JS */
/* In single focus, the marketplaces section is hidden via JS; ensure no residual gap below the upper divider */
.payout-cards-row .marketplaces-div.single-marketplace-active { margin-top: 0 !important; padding-top: 0 !important; height: 0 !important; }
/* Reset spacing when returning to ALL focus so no residual gaps remain */
.payout-cards-row .marketplaces-div:not(.single-marketplace-active) { margin-top: 0 !important; padding-top: 0 !important; height: auto !important; }
.payout-cards-row .marketplaces-div:not(.single-marketplace-active) .marketplaces-payout-row { margin-top: 0 !important; }
.marketplace-payout-amount {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 11px;
  color: var(--text-primary);
  text-align: center;
}

/* Hide taxes item for non-US marketplaces */
.payout-data-item.taxes-item {
  display: block;
}

/* Net earnings comparison styling */
.net-earnings-with-compare {
  display: flex;
  align-items: center;
  gap: 6px;
}

.net-earnings-compare {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: help;
}

.net-earnings-compare .comparison-percentage {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 10px;
  font-weight: 700;
  line-height: 10px;
}

.net-earnings-compare .comparison-arrow {
  width: 12px;
  height: 8px;
  flex-shrink: 0;
}

.net-earnings-compare .comparison-percentage.positive {
  color: #04AE2C;
}

.net-earnings-compare .comparison-percentage.negative {
  color: #FF391F;
}

.net-earnings-compare .comparison-percentage.neutral {
  color: #606F95;
}

/* Dark theme support for net earnings comparison */
[data-theme="dark"] .net-earnings-compare .comparison-percentage.positive {
  color: #04AE2C;
}

[data-theme="dark"] .net-earnings-compare .comparison-percentage.negative {
  color: #FF391F;
}

[data-theme="dark"] .net-earnings-compare .comparison-percentage.neutral {
  color: #FFFFFF;
}

/* Dark theme support for payout section */
[data-theme="dark"] .next-payout-card-div,
[data-theme="dark"] .previous-payout-card-div {
  background: var(--bg-primary);
}

[data-theme="dark"] .payout-card-title {
  color: var(--text-primary);
}

[data-theme="dark"] .payout-date {
  color: #FFFFFF;
}

[data-theme="dark"] .marketplace-payout-amount {
  color: var(--text-primary);
}

/* Value styling for positive/negative amounts */
.payout-data-value.positive {
  color: #04AE2C;
}

.payout-data-value.negative {
  color: #FF391F;
}

[data-theme="dark"] .payout-data-value.positive {
  color: #04AE2C;
}

[data-theme="dark"] .payout-data-value.negative {
  color: #FF391F;
}
[data-theme="dark"] .lifetime-data-label { color: #FFFFFF; }
.lifetime-data-value {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  color: #606F95;
}
.lifetime-data-value.zero { color: #606F95 !important; opacity: 0.4; }
[data-theme="dark"] .lifetime-data-value.zero { color: #B4B9C5 !important; opacity: 0.4; }
.lifetime-data-value.positive { color: #04AE2C; }
.lifetime-data-value.negative { color: #FF391F; }
[data-theme="dark"] .lifetime-data-value.revenue,
[data-theme="dark"] .lifetime-data-value.gross-earnings { color: var(--text-primary); }
[data-theme="dark"] .lifetime-data-value.negative .percentage { color: #FFFFFF; }
.lifetime-data-value.negative .percentage { color: #606F95; }

/* Feedback card compact reviews list */
.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 0;
  width: 100%;
  max-height: 280px;
  overflow-y: auto;
  /* Match Today's card custom scrollbar behavior */
  padding-right: 0; /* no extra right padding until hover */
}
.reviews-list:hover { padding-right: 8px; }
.reviews-list::-webkit-scrollbar { width: 6px; height: 6px; }
.reviews-list::-webkit-scrollbar:horizontal { display: none; }
.reviews-list::-webkit-scrollbar-track { background: transparent; margin-left: 6px; }
.reviews-list::-webkit-scrollbar-thumb { background: transparent; border-radius: 6px; }
.reviews-list:hover::-webkit-scrollbar-thumb, .reviews-list:focus-within::-webkit-scrollbar-thumb { background: #D9DDEB; }
.reviews-list::-webkit-scrollbar-thumb:hover { background: #E9EBF2; }
@supports (scrollbar-width: thin) {
  .reviews-list { scrollbar-width: thin; scrollbar-color: transparent transparent; }
  .reviews-list:hover, .reviews-list:focus-within { scrollbar-color: #D9DDEB transparent; }
}
[data-theme="dark"] .reviews-list::-webkit-scrollbar-thumb { background: transparent; }
[data-theme="dark"] .reviews-list:hover::-webkit-scrollbar-thumb, [data-theme="dark"] .reviews-list:focus-within::-webkit-scrollbar-thumb { background: #2F3341; }
@supports (scrollbar-width: thin) {
  [data-theme="dark"] .reviews-list { scrollbar-color: transparent transparent; }
  [data-theme="dark"] .reviews-list:hover,
  [data-theme="dark"] .reviews-list:focus-within { scrollbar-color: #2F3341 transparent; }
}
/* 3-Column Layout: Left (product), Middle (content), Right (view button) */
.reviews-list .listing-review-div {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  gap: 16px;
  width: 100%;
  min-width: 0;
}

/* Left Column: Product Image */
.reviews-list .product-div {
  width: 75px;
  height: 100px;
  min-width: 75px;
  max-width: 75px;
  flex-shrink: 0;
  position: relative;
  background: #E9EBF2;
  border-radius: 8px;
  overflow: hidden;
}
.reviews-list .listing-product-img {
  width: 75px;
  height: 100px;
  min-width: 75px;
  max-width: 75px;
  flex-shrink: 0;
  border-radius: 8px;
  object-fit: cover;
}

/* Reviews product-div states mimic listing-left-div */
.reviews-list .product-div .preloader,
.reviews-list .product-div .listing-product-img { display: none; }

.reviews-list .product-div.state-loaded .listing-product-img { display: block; }

.reviews-list .product-div .preloader {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-width: 32px;
  min-height: 40px;
  background: #E9EBF2 url('./assets/snap-loader-ic.svg') center no-repeat;
  background-size: 32px 40px;
  position: relative;
  overflow: hidden;
}
.reviews-list .product-div .preloader::before {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 150%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 20%,
    rgba(255, 255, 255, 0.3) 40%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.3) 60%,
    rgba(255, 255, 255, 0.1) 80%,
    transparent 100%
  );
  animation: skeleton-shimmer 1.5s ease-in-out infinite alternate;
}

.reviews-list .product-div.state-private { background: #470CED; position: relative; overflow: hidden; }
.reviews-list .product-div.state-private .preloader { display: none; }
.reviews-list .product-div.state-loaded { border-radius: 8px; }

/* Mirror today's card private-state visuals */
.reviews-list .product-div.state-private::before {
  content: '';
  position: absolute;
  top: 12px;
  left: 12px;
  width: 24px;
  height: 24px;
  background-image: url('./assets/privacy-mode-ic.svg');
  background-size: contain;
  background-repeat: no-repeat;
  z-index: var(--z-surface);
}
.reviews-list .product-div.state-private::after {
  content: 'Privacy Mode';
  position: absolute;
  bottom: 12px;
  left: 12px;
  right: 12px;
  color: #FFFFFF;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.2;
  text-align: left;
  z-index: var(--z-surface);
}

[data-theme="dark"] .reviews-list .product-div .preloader,
[data-theme="dark"] .reviews-list .product-div.state-loading .preloader {
  background: var(--border-color) url('./assets/snap-loader-ic.svg') center no-repeat;
  background-size: 32px 40px;
}
[data-theme="dark"] .reviews-list .product-div .preloader::before {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.05) 20%,
    rgba(255, 255, 255, 0.08) 40%,
    rgba(255, 255, 255, 0.12) 50%,
    rgba(255, 255, 255, 0.08) 60%,
    rgba(255, 255, 255, 0.05) 80%,
    transparent 100%
  );
}

/* Middle Column: Content Stack */
.reviews-list .listing-content-div {
  flex: 1 1 auto;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* Right Column: View Button */
.reviews-list .view-btn {
  flex-shrink: 0;
  align-self: flex-start;
}

/* Right Column: Actions column containing View (top) and Edit/Analyse (bottom) */
.reviews-list .review-actions-col {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  align-self: stretch;
  gap: 8px;
}
.reviews-list .review-actions-col .view-btn { margin-left: 0; align-self: flex-end; }
/* Remove old title-marketplace-div rules - no longer needed with new structure */
.reviews-list .title-and-marketplace {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  min-width: 0;
}
.reviews-list .listing-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: #606F95;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}
.reviews-list .listing-comment {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 13px;
  color: #606F95;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  margin-top: 6px;
  box-sizing: border-box;
}
[data-theme="dark"] .reviews-list .listing-comment { color: #FFFFFF; }

/* Rating stars under comment – inline SVG based */
.rating-stars { display: inline-flex; align-items: center; gap: 6px; margin-top: 6px; line-height: 0; }
.rating-stars .star { width: 14px; height: 14px; display: block; }
.rating-stars .star path { stroke-linejoin: round; stroke-linecap: round; }
.rating-row { display: flex; align-items: center; gap: 8px; margin-top: 4px; }
.rating-row .rating-stars { margin-top: 0; height: 14px; }
.rating-row .rating-stars .star { height: 14px; }
.rating-text {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #606F95;
  height: 14px; /* match star height for perfect centering */
  line-height: 14px; /* vertically centers text within the 14px box */
  display: inline-flex;
  align-items: center;
}
/* Listing right column rating row (Today/Yesterday cards) */
.listing-rating-row { display:flex; align-items:center; gap:8px; margin-top:8px; }
.listing-rating-row .listing-reviews-count { font-family:'Amazon Ember',sans-serif; font-size:12px; color:#606F95; }
.listing-rating-row .rating-stars { margin-top:0; }
.listing-rating-row .rating-text { display:none !important; }
/* Remove extra top spacing in Today's/Yesterday's cards only */
.todays-sales-card-div .listing-rating-row,
.yesterdays-sales-card-div .listing-rating-row { margin-top: 0; }
.review-date { margin-top: 8px; font-family: 'Amazon Ember', sans-serif; font-weight: 600; font-size: 12px; color: #606F95; }
[data-theme="dark"] .rating-text, [data-theme="dark"] .review-date { color: var(--text-primary); }
[data-theme="dark"] .listing-rating-row .listing-reviews-count { color: var(--text-primary); }
/* Remove any dark-mode background behind stars to avoid grey artifacts */
[data-theme="dark"] .rating-stars .star { background-color: transparent !important; }

/* View button for reviews list (customer feedback card) */
.view-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 5px 10px;
  height: 32px !important;
  background: #FFFFFF;
  border: 1px solid #E9EBF2;
  border-radius: 4px;
  cursor: pointer;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #606F95;
  box-sizing: border-box; /* ensure total height = 32 incl. padding + border */
}
.reviews-list .view-btn { margin-left: auto; }
.view-btn img { order: 2; }
.view-btn span { order: 1; }
.view-btn * { line-height: 1; }
.view-btn:hover { background: #F7F8FA; border-color: #DCE0E5; }
[data-theme="dark"] .view-btn { background: var(--bg-primary); border-color: var(--border-color); color: var(--text-primary); }
[data-theme="dark"] .view-btn:hover { background: var(--btn-hover); border-color: var(--border-color); }

/* Reviews card: add bottom spacing to each list divider */
.customer-reviews-card-div .listing-section-divider { margin-bottom: 24px; }

/* Remove fixed height for the 4 new sales cards */
.four-sales-cards-section .current-month-card-div,
.four-sales-cards-section .last-month-card-div,
.four-sales-cards-section .current-year-card-div,
.four-sales-cards-section .last-year-card-div {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
  min-width: 0 !important; /* Override global min-width to prevent horizontal scrolling */
}

/* Remove fixed height for the top 4 new sales cards */
.top-four-sales-cards-section .top-day-card-div,
.top-four-sales-cards-section .top-week-card-div,
.top-four-sales-cards-section .top-month-card-div,
.top-four-sales-cards-section .top-year-card-div {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
  min-width: 0 !important; /* Override global min-width to prevent horizontal scrolling */
}

/* Enhanced marketplace opacity styling that works with filter states */
/* Zero sales styling - works in conjunction with active/inactive states */

/* Default state: marketplace with sales */
.marketplace-col {
  transition: opacity 0.3s ease, filter 0.3s ease;
}

/* Zero sales marketplaces - always 50% opacity regardless of filter state */
.marketplace-col.zero-sales {
  opacity: 0.5 !important;
}

/* Zero sales + inactive state - maintain 50% opacity but add grayscale */
.marketplace-col.zero-sales.inactive {
  opacity: 0.5 !important;
  filter: grayscale(100%);
}

/* Zero sales + inactive state on hover - remove grayscale but keep 50% opacity */
.marketplace-col.zero-sales.inactive:hover {
  opacity: 0.5 !important;
  filter: none;
}
/* Zero reviews marketplaces - mirror zero-sales behavior */
.marketplace-col.zero-reviews { opacity: 0.5 !important; }
.marketplace-col.zero-reviews.inactive { opacity: 0.5 !important; filter: grayscale(100%); }
.marketplace-col.zero-reviews.inactive:hover { opacity: 0.5 !important; filter: none; }
.sales-count,
.reviews-count {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 800;
  font-size: 48px;
  color: #470CED;
  line-height: 1.2;
  text-align: right;
}
[data-theme="dark"] .sales-count.zero,
.sales-count.zero,
[data-theme="dark"] .reviews-count.zero,
.reviews-count.zero {
  color: var(--text-secondary, #9aa3ab) !important;
  opacity: 0.9 !important;
}
[data-theme="dark"] .sales-count, [data-theme="dark"] .reviews-count { color: #470CED; }
.analytics-div {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  width: 473px;
  gap: 0;
  margin-top: 10px;
}

/* Feedback card compact metrics next to sales count */
.comments-ratings-div {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 8px;
}
.metric-col {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  /* Remove right padding */
  padding-right: 0;
  min-width: 0;
}

/* New horizontal layout for metric label and percentage */
.metric-label-row {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
}
.metric-top {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
}
.metric-value {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: #606F95;
  line-height: 1.2;
  margin-top: 2px;
}
.metric-value.royalties {
  color: #04AE2C;
}
.metric-value.royalties.positive {
  color: #04AE2C !important;
}
.metric-value.royalties.negative {
  color: #FF391F !important;
}
.metric-value.returned {
  color: #FF391F;
  white-space: nowrap;
}
.metric-value.zero {
  color: #606F95 !important;
}
.metric-value.cancelled.has-value {
  color: #FDC300 !important;
}
.metric-value.ads.has-value,
.metric-value.new.has-value {
  color: #04AE2C !important;
}
.metric-value.new.negative,
.metric-value.ads.negative,
.metric-value.cancelled.negative {
  color: #FF391F !important;
}
.metric-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  color: #606F95;
  line-height: 1.2;
}

/* Metric Percentage Styles */
.metric-percentage {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 600;
  font-size: 11px;
  color: #606F95;
  line-height: 1.2;
  /* Removed margin-top for inline horizontal layout */
}

[data-theme="dark"] .metric-percentage {
  color: #B4B9C5;
}

.metric-percentage.zero {
  color: #606F95 !important;
  display: none; /* Hide zero percentages globally */
}

[data-theme="dark"] .metric-percentage.zero {
  color: #B4B9C5 !important;
  display: none; /* Hide zero percentages globally (dark theme) */
}

/* New and Ads percentage colors */
.metric-percentage.new-percentage,
.metric-percentage.ads-percentage {
  color: #04AE2C !important;
}

[data-theme="dark"] .metric-percentage.new-percentage,
[data-theme="dark"] .metric-percentage.ads-percentage {
  color: #04AE2C !important;
}

/* Returns percentage color */
.metric-percentage.returned-percentage {
  color: #FF391F !important;
}

[data-theme="dark"] .metric-percentage.returned-percentage {
  color: #FF391F !important;
}

/* Negative percentage styling */
.metric-percentage.negative {
  color: #FF391F !important;
}

[data-theme="dark"] .metric-percentage.negative {
  color: #FF391F !important;
}
.metric-icon {
  width: 12px;
  height: 12px;
  display: inline-block;
}
.metric-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1px;
  height: 35px;
  background: #606F95;
  opacity: 0.1;
  margin: 0 8px;
}
@media (max-width: 900px) {
  .analytics-div {
    width: 100%;
    gap: 0;
  }
  .sales-analytics-div {
    flex-direction: column;
    align-items: stretch;
  }
  .sales-count-div {
    padding-right: 0;
    margin-bottom: 16px;
    margin-right: 0;
    justify-content: flex-start;
  }
}
[data-theme="dark"] .metric-value.royalties {
  color: #04AE2C;
}
[data-theme="dark"] .metric-value.royalties.positive {
  color: #04AE2C !important;
}
[data-theme="dark"] .metric-value.royalties.negative {
  color: #FF391F !important;
}
[data-theme="dark"] .metric-value.returned {
  color: #FF391F;
}
[data-theme="dark"] .metric-value,
[data-theme="dark"] .metric-label {
  color: #B4B9C5;
}
[data-theme="dark"] .metric-value.zero {
  color: #B4B9C5 !important;
}
[data-theme="dark"] .metric-value.cancelled.has-value {
  color: #FDC300 !important;
}
[data-theme="dark"] .metric-value.ads.has-value,
[data-theme="dark"] .metric-value.new.has-value {
  color: #04AE2C !important;
}
[data-theme="dark"] .metric-value.new.negative,
[data-theme="dark"] .metric-value.ads.negative,
[data-theme="dark"] .metric-value.cancelled.negative {
  color: #FF391F !important;
}
[data-theme="dark"] .metric-divider {
  background: #B4B9C5;
  opacity: 0.1;
}

/* Section Dividers (sales + reviews + payout use identical styling) */
.sales-section-divider,
.reviews-section-divider,
.payout-section-divider {
  width: 100%;
  border: 0.75px solid #E9EBF2;
  opacity: 0.5;
  margin: 24px -24px 24px -24px; /* Negative margins to extend beyond parent padding on both sides */
  flex-shrink: 0;
}
[data-theme="dark"] .sales-section-divider,
[data-theme="dark"] .reviews-section-divider,
[data-theme="dark"] .payout-section-divider {
  border: 0.75px solid #292d3f;
}

/* payout-section-divider has been removed from HTML structure */

/* Fix divider width inside payout cards to match lifetime insights card */
.payout-cards-row .sales-section-divider,
.payout-cards-row .payout-section-divider {
  margin-left: -24px !important;
  margin-right: -24px !important;
  width: auto !important;
  margin-top: 0 !important; /* Remove top gap */
  margin-bottom: 0 !important; /* Remove bottom gap */
}

/* Make "After Taxes" payout data item display label above value */
.payout-data-item.taxes-item {
  display: flex !important;
  flex-direction: column !important;
  gap: 6px !important;
  align-items: flex-start !important;
}

/* Hide taxes item for non-US marketplaces - must come after flex rule to override */
.payout-data-item.taxes-item.hidden {
  display: none !important;
}

/* Make label and percentage display horizontally with gap */
.taxes-label-row {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Payout Summary Section - matches sales-count styling */
.payout-summary-div {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0px 32px 0px 0;
}

.payout-summary-div .payout-summary-count + .comparison-container { margin-left: 16px; }

.payout-summary-count {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 800;
  font-size: 32px;
  color: var(--text-primary);
  line-height: 1.2;
  text-align: left;
  display: inline-flex;
  align-items: baseline;
  gap: 4px;
}

/* Payout summary state colors */
.payout-summary-count.positive { color: var(--text-primary); }
.payout-summary-count.zero { color: #606F95; opacity: 0.4; }

/* Dark theme support for payout summary */
[data-theme="dark"] .payout-summary-count.positive { color: var(--text-primary); }
[data-theme="dark"] .payout-summary-count.zero { color: #B4B9C5; opacity: 0.4; }

/* Currency-only green styling for payout summary */
.payout-summary-count .payout-summary-currency { color: #04AE2C; }
[data-theme="dark"] .payout-summary-count .payout-summary-currency { color: #04AE2C; }

/* When value is zero, currency should not be green; inherit the zero color */
.payout-summary-count.zero .payout-summary-currency { color: inherit; }
[data-theme="dark"] .payout-summary-count.zero .payout-summary-currency { color: inherit; }

/* Marketplaces Div (Figma accurate) */
.marketplaces-div {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  min-width: 1024px;
}
.marketplaces-sales-row,
.marketplaces-reviews-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 0;
  width: 100%;
  min-width: 1024px;
}
.marketplace-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0px;
  /* Remove min-width and flex so width is determined by children */
  min-width: unset;
  flex: unset;
  cursor: pointer;
  transition: opacity 0.3s ease, transform 0.2s ease;
  will-change: transform;
  border-radius: 8px;
  padding: 0 8px; /* left/right only by default */
  position: relative;
}

.marketplace-col:hover {
  transform: translateY(-2px);
  background: rgba(96, 111, 149, 0.05);
}

.marketplace-col.all-marketplaces.active:hover,
.single-marketplace-active .marketplace-col.active:hover,
.marketplace-col.active:hover {
  transform: none;
  background: transparent;
}

[data-theme="dark"] .marketplace-col:hover {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .marketplace-col.all-marketplaces.active:hover,
[data-theme="dark"] .single-marketplace-active .marketplace-col.active:hover,
[data-theme="dark"] .marketplace-col.active:hover {
  background: transparent;
}

/* Marketplace filter states - updated to work with zero-sales styling */
.marketplace-col.active {
  opacity: 1;
}

/* Inactive marketplaces with sales - 50% opacity + grayscale */
.marketplace-col.inactive:not(.zero-sales) {
  opacity: 0.5;
}

.marketplace-col.inactive .marketplace-icon {
  filter: grayscale(100%);
}

/* Active marketplaces with zero sales - still 50% opacity but no grayscale */
.marketplace-col.active.zero-sales {
  opacity: 0.5 !important;
}

.marketplace-col.active.zero-sales .marketplace-icon {
  filter: none;
}

/* Remove opacity and grayscale from inactive marketplace columns on hover */
/* But respect zero-sales opacity */
.marketplace-col.inactive:not(.zero-sales):hover {
  opacity: 1;
}

.marketplace-col.inactive:hover .marketplace-icon {
  filter: none;
}

/* Zero-sales marketplaces on hover - remove grayscale but keep 50% opacity */
.marketplace-col.zero-sales:hover {
  opacity: 0.5 !important;
}

/* Custom tooltips for marketplace columns are handled by JavaScript */
/* This ensures the default tooltips don't interfere */
.marketplace-col[data-tooltip]:hover:before,
.marketplace-col[data-tooltip]:hover:after {
  display: none !important;
}

/* Force hide tooltips when marketplace column is clicked/focused */
.marketplace-col[data-tooltip]:focus:before,
.marketplace-col[data-tooltip]:focus:after {
  opacity: 0 !important;
  visibility: hidden !important;
}

/* Also hide on active state to prevent tooltip persistence */
.marketplace-col[data-tooltip]:active:before,
.marketplace-col[data-tooltip]:active:after {
  opacity: 0 !important;
  visibility: hidden !important;
}

/* Ensure smooth transitions for all marketplace elements */
.marketplace-col * {
  transition: var(--theme-transition);
}

/* Focus states for accessibility */
.marketplace-col:focus {
  outline: none;
}
.marketplace-icon {
  width: 28px;
  height: 28px;
  margin-bottom: 2px;
}
.marketplace-total-sales-count,
.marketplace-total-reviews-count {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: #606F95;
}
/* Customer Reviews card: add top padding to marketplace review counts */
.customer-reviews-card-div .marketplace-total-reviews-count { padding-top: 6px; display: inline-block; }
.marketplace-total-sales-count {
  padding-top: 6px;
}
.marketplace-total-sales-count.zero,
.marketplace-total-reviews-count.zero {
  color: #606F95;
}
.marketplace-total-sales-count.negative,
.marketplace-total-reviews-count.negative {
  color: #FF391F;
}
.marketplace-total-earned-royalties {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 11px;
  color: #04AE2C;
}
.marketplace-total-earned-royalties.positive {
  color: #04AE2C;
}
.marketplace-total-earned-royalties.zero {
  color: #606F95;
}
.marketplace-total-earned-royalties.negative {
  color: #FF391F;
}
.marketplace-total-returned-units {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 11px;
  color: #FF391F;
}
.marketplace-total-returned-units.zero {
  color: #606F95;
}
.marketplace-total-returned-units.negative {
  color: #FF391F;
}
@media (max-width: 900px) {
  .marketplaces-sales-row {
    flex-wrap: nowrap !important;
    gap: 0;
  }
  .marketplace-col {
    min-width: 0;
    flex: unset;
  }
}

/* Keep full padding for Today's and Yesterday's cards only */
.todays-sales-card-div .marketplace-col,
.yesterdays-sales-card-div .marketplace-col {
  padding: 8px; /* all around */
}
[data-theme="dark"] .marketplace-total-sales-count,
[data-theme="dark"] .marketplace-total-reviews-count {
  color: #B4B9C5;
}
[data-theme="dark"] .marketplace-total-sales-count.zero,
[data-theme="dark"] .marketplace-total-reviews-count.zero {
  color: #B4B9C5;
}
[data-theme="dark"] .marketplace-total-sales-count.negative,
[data-theme="dark"] .marketplace-total-reviews-count.negative {
  color: #FF391F;
}
[data-theme="dark"] .marketplace-total-earned-royalties {
  color: #04AE2C;
  font-size: 11px;
}
[data-theme="dark"] .marketplace-total-earned-royalties.positive {
  color: #04AE2C;
}
[data-theme="dark"] .marketplace-total-earned-royalties.zero {
  color: #B4B9C5;
}
[data-theme="dark"] .marketplace-total-earned-royalties.negative {
  color: #FF391F;
}
[data-theme="dark"] .marketplace-total-returned-units {
  color: #FF391F;
}
[data-theme="dark"] .marketplace-total-returned-units.zero {
  color: #B4B9C5;
}
[data-theme="dark"] .marketplace-total-returned-units.negative {
  color: #FF391F;
}

.search-tabs-div {
  width: 100%;
  flex-direction: column;
  gap: 10px;
  margin-top: 24px;
  min-width: 1024px;
}
.search-div {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
}
/* Dashboard search input wrapper - more specific to override data-grid CSS */
.todays-sales-card-div .search-input-wrapper,
.yesterdays-sales-card-div .search-input-wrapper,
.current-month-card-div .search-input-wrapper,
.last-month-card-div .search-input-wrapper,
.current-year-card-div .search-input-wrapper,
.last-year-card-div .search-input-wrapper {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  background: #fff;
  border: 1.5px solid #E9EBF2;
  border-radius: 8px;
  padding: 10px;
  width: 100%;
  min-width: 0;
  box-sizing: border-box;
  gap: 8px;
  position: static !important;
}
[data-theme="dark"] .todays-sales-card-div .search-input-wrapper,
[data-theme="dark"] .yesterdays-sales-card-div .search-input-wrapper,
[data-theme="dark"] .current-month-card-div .search-input-wrapper,
[data-theme="dark"] .last-month-card-div .search-input-wrapper,
[data-theme="dark"] .current-year-card-div .search-input-wrapper,
[data-theme="dark"] .last-year-card-div .search-input-wrapper {
  background: var(--bg-primary);
  border: 1.5px solid var(--border-color);
}
/* Dashboard search sales icon - more specific to override data-grid CSS */
.todays-sales-card-div .search-sales-icon,
.yesterdays-sales-card-div .search-sales-icon,
.current-month-card-div .search-sales-icon,
.last-month-card-div .search-sales-icon,
.current-year-card-div .search-sales-icon,
.last-year-card-div .search-sales-icon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
  position: static !important;
  left: auto !important;
  top: auto !important;
  transform: none !important;
}
/* Dashboard search input - more specific to override data-grid CSS */
.todays-sales-card-div .search-input,
.yesterdays-sales-card-div .search-input,
.current-month-card-div .search-input,
.last-month-card-div .search-input,
.current-year-card-div .search-input,
.last-year-card-div .search-input {
  border: none;
  outline: none;
  background: transparent;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  color: #606F95;
  flex: 1 1 auto;
  min-width: 0;
  padding-left: 0 !important;
}

.search-input::placeholder {
  color: var(--color-text-placeholder);
  opacity: 0.5;
}

[data-theme="dark"] .search-input {
  color: #B4B9C5;
}

[data-theme="dark"] .search-input::placeholder {
  color: var(--color-text-placeholder);
  opacity: 0.7;
}
.close-search-icon {
  width: 19px;
  height: 19px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.close-search-icon:hover {
  opacity: 0.7 !important;
}

.search-input:focus + .close-search-icon {
  opacity: 1;
}

/* Focus state styling for search-input-wrapper in today's and yesterday's sales cards */
.todays-sales-card-div .search-input-wrapper:focus-within,
.yesterdays-sales-card-div .search-input-wrapper:focus-within {
  border-color: #470CED;
  outline: none !important;
  box-shadow: none !important;
}

.todays-sales-card-div .search-input-wrapper:focus-within .search-input,
.yesterdays-sales-card-div .search-input-wrapper:focus-within .search-input {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

[data-theme="dark"] .todays-sales-card-div .search-input-wrapper:focus-within,
[data-theme="dark"] .yesterdays-sales-card-div .search-input-wrapper:focus-within {
  border-color: #470CED;
  outline: none !important;
  box-shadow: none !important;
}

[data-theme="dark"] .todays-sales-card-div .search-input-wrapper:focus-within .search-input,
[data-theme="dark"] .yesterdays-sales-card-div .search-input-wrapper:focus-within .search-input {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

.sales-filter-div {
  width: 100%;
  height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  background: rgba(232, 235, 244, 0.5);
  border-radius: 6px;
  padding: 3px;
  gap: 3px;
  box-sizing: border-box;
}
[data-theme="dark"] .sales-filter-div {
  background: #292E38;
}
.sales-filter-tab {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 0 8px;
  border-radius: 4px;
  cursor: pointer;
  background: transparent;
  height: 34px;
  flex: 1 1 0;
  justify-content: center;
  min-width: 0;
  position: relative;
}
.sales-filter-tab.active {
  background: #fff;
  box-shadow: 0 2px 8px rgba(96, 111, 149, 0.04);
}

/* Hover state for inactive sales filter tabs only */
.sales-filter-tab:not(.active):hover {
  background: rgba(96, 111, 149, 0.04);
  transition: background 0.2s ease;
}

[data-theme="dark"] .sales-filter-tab.active {
  background: var(--bg-primary);
  box-shadow: none;
}

/* Dark theme hover state for inactive sales filter tabs only */
[data-theme="dark"] .sales-filter-tab:not(.active):hover {
  background: rgba(255, 255, 255, 0.05);
}
.tab-icon {
  width: 16px;
  height: 16px;
}
.tab-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 11px;
  color: #606F95;
  display: block; /* Show text labels */
  margin-top: 2px;
}
[data-theme="dark"] .tab-label {
  color: #B4B9C5;
}
.tab-sort-icon {
  width: 14px;
  height: 14px;
  margin-left: 2px;
}
@media (max-width: 900px) {
  .search-tabs-div {
    min-width: 0;
    width: 100%;
  }
  .search-input-wrapper {
    width: 100%;
    min-width: 0;
  }
  .sales-filter-div {
    flex-wrap: wrap;
    width: 100%;
  }
}

.tab-main {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  justify-content: center;
  flex: 1 1 auto;
  min-width: 0;
  position: absolute;
  left: 0;
  right: 0;
  height: 100%;
}
.tab-sort-icon {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: var(--z-base);
}
.tab-sort-icon.active {
  opacity: 1;
  pointer-events: auto;
}

/* Fix tooltip hover detection for all tabs with tooltips */
.sales-filter-tab .tab-main,
.sales-filter-tab .tab-label,
.sales-filter-tab .tab-sort-icon {
  pointer-events: none;
}

/* Ensure the parent tab remains clickable */
.sales-filter-tab {
  pointer-events: auto;
}

/* Fix tooltip hover detection for privacy mode toggle tabs */
.privacy-mode-toggle .off-tab .off-div,
.privacy-mode-toggle .off-tab .off-div span,
.privacy-mode-toggle .off-tab .off-div img,
.privacy-mode-toggle .on-tab .on-div,
.privacy-mode-toggle .on-tab .on-div span,
.privacy-mode-toggle .on-tab .on-div img {
  pointer-events: none;
}

/* Ensure the parent privacy toggle tabs remain clickable */
.privacy-mode-toggle .off-tab,
.privacy-mode-toggle .on-tab {
  pointer-events: auto;
}

/* Fix tooltip hover detection for listing status cards */
.listing-status-card .listing-status-top,
.listing-status-card .listing-status-icon,
.listing-status-card .listing-status-icon img,
.listing-status-card .listing-status-number,
.listing-status-card .listing-status-label {
  pointer-events: none;
}

/* Ensure the parent listing status cards remain clickable */
.listing-status-card {
  pointer-events: auto;
}

/* Global fix for all tabs with tooltips - prevent child elements from blocking hover */
/* Target sales filter tabs and similar tab structures */
[data-tooltip].sales-filter-tab *,
[data-tooltip].time-tab *,
[data-tooltip].units-tab *,
[data-tooltip].royalties-tab *,
[data-tooltip].new-tab *,
[data-tooltip].ad-spend-tab *,
[data-tooltip][class$="-tab"]:not([class*="off-tab"]):not([class*="on-tab"]) * {
  pointer-events: none;
}

/* Ensure parent tabs with tooltips remain clickable */
[data-tooltip].sales-filter-tab,
[data-tooltip].time-tab,
[data-tooltip].units-tab,
[data-tooltip].royalties-tab,
[data-tooltip].new-tab,
[data-tooltip].ad-spend-tab,
[data-tooltip][class$="-tab"]:not([class*="off-tab"]):not([class*="on-tab"]) {
  pointer-events: auto;
}

/* Fix tooltip hover detection for comparison label and control buttons */
/* Ensure hovering over any child still triggers the parent's tooltip */
[data-tooltip].comparison-label *,
[data-tooltip].comparison-container *,
[data-tooltip].privacy-toggle-btn *,
[data-tooltip].dashboard-refresh-btn * {
  pointer-events: none;
}

/* Also support when comparison elements are inside a tooltip-enabled ancestor */
[data-tooltip] .comparison-label *,
[data-tooltip] .comparison-content *,
[data-tooltip] .comparison-container * {
  pointer-events: none;
}

/* Keep the parent elements interactive */
[data-tooltip].comparison-label,
[data-tooltip].comparison-container,
[data-tooltip].privacy-toggle-btn,
[data-tooltip].dashboard-refresh-btn {
  pointer-events: auto;
}

/* Ensure all direct children of main-content and sales card divs never overflow parent */
.main-content > *,
.todays-sales-card-div > *,
.yesterdays-sales-card-div > *,
.current-month-card-div > *,
.last-month-card-div > *,
.current-year-card-div > *,
.last-year-card-div > * {
  width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box;
}

/* Fix divider width - account for dynamic scrollbar padding */
.todays-sales-card-div > .sales-section-divider,
.yesterdays-sales-card-div > .sales-section-divider,
.current-month-card-div > .sales-section-divider,
.last-month-card-div > .sales-section-divider,
.current-year-card-div > .sales-section-divider,
.last-year-card-div > .sales-section-divider,
.top-day-card-div > .sales-section-divider,
.top-week-card-div > .sales-section-divider,
.top-month-card-div > .sales-section-divider,
.top-year-card-div > .sales-section-divider {
  margin-left: -24px !important; /* Always extend to left edge */
  margin-right: -24px !important; /* Default: extend to right edge when no scrollbar */
  width: auto !important; /* Let margins determine width */
  min-width: unset !important; /* Remove any width constraints */
  max-width: none !important; /* Remove any width constraints */
}

/* Match divider behavior in Lifetime Insights card to top-month-card-div */
.insights-and-feedback-section .lifetime-insights-card-div > .sales-section-divider,
.insights-and-feedback-section .customer-reviews-card-div > .sales-section-divider,
.insights-and-feedback-section .customer-reviews-card-div > .reviews-section-divider {
  margin-left: -24px !important;
  margin-right: -24px !important;
  width: auto !important;
  min-width: unset !important;
  max-width: none !important;
}

/* Products page layout ensures the grid fills the viewport */
.products-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.products-grid-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  margin-right: 12px;
}

.products-grid {
  flex: 1;
  min-height: 0;
  width: 100%;
  position: relative;
}

/* When scrollbar is visible, card has padding-right: 10px, so divider needs margin-right: -10px */
.todays-sales-card-div.scrollbar-visible > .sales-section-divider,
.yesterdays-sales-card-div.scrollbar-visible > .sales-section-divider,
.current-month-card-div.scrollbar-visible > .sales-section-divider,
.last-month-card-div.scrollbar-visible > .sales-section-divider,
.current-year-card-div.scrollbar-visible > .sales-section-divider,
.last-year-card-div.scrollbar-visible > .sales-section-divider {
  margin-right: -10px !important;
}

/* When scrollbar is hidden, card has padding-right: 24px, so divider needs margin-right: -24px */
.todays-sales-card-div.scrollbar-hidden > .sales-section-divider,
.yesterdays-sales-card-div.scrollbar-hidden > .sales-section-divider,
.current-month-card-div.scrollbar-hidden > .sales-section-divider,
.last-month-card-div.scrollbar-hidden > .sales-section-divider,
.current-year-card-div.scrollbar-hidden > .sales-section-divider,
.last-year-card-div.scrollbar-hidden > .sales-section-divider {
  margin-right: -24px !important;
}

/* Prevent any child elements from causing horizontal overflow in sales cards */
.todays-sales-card-div *,
.yesterdays-sales-card-div *,
.current-month-card-div *,
.last-month-card-div *,
.current-year-card-div *,
.last-year-card-div *,
.top-day-card-div *,
.top-week-card-div *,
.top-month-card-div *,
.top-year-card-div * {
  max-width: 100%;
  box-sizing: border-box;
}

/* Remove min-width from children of Sales-card-div and main-content */
.marketplaces-div,
.search-tabs-div,
.sales-analytics-div {
  min-width: 0 !important;
  width: 100% !important;
}

/* Ensure marketplaces rows always fit parent width */
.marketplaces-sales-row,
.marketplaces-reviews-row {
  width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box;
}

/* --- Listing Analytics Section Styles --- */
.listing-analytics-div {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 20px;
  width: 100%;
  min-width: 0;
  margin-top: 24px;
  padding: 0;
  box-sizing: border-box;
  flex-wrap: nowrap;
  /* Smooth transitions for reordering */
  transition: opacity 0.15s ease-in-out, transform 0.15s ease-in-out;
}
.listing-left-div {
  width: 75px;
  height: 100px;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  background: none;
  border-radius: 8px;
}
.listing-product-img {
  width: 75px;
  height: 100px;
  border-radius: 8px;
  background: #000000; /* Default black background - will be overridden by JavaScript */
  position: absolute;
  top: 0;
  left: 0;
  z-index: var(--z-base);
  object-fit: cover;
  box-sizing: border-box; /* Ensure border doesn't affect dimensions */
}
.listing-right-div {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 160px;
  max-width: none;
  flex: 0 1 auto;
  gap: 12px;
}

.listing-middle-div {
  display: flex;
  flex-direction: column;
  flex: 1 1 0;
  min-width: 0;
  gap: 10px;
}
.listing-title-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 0;
  min-width: 0;
  position: relative;
}
.listing-badge-ic {
  width: 64px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 0 0 auto;
  align-self: center;
  margin: 0;
}
.listing-title-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  min-width: 0;
  flex: 1 1 0;
  flex-wrap: wrap;
}
.listing-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 13px;
  color: #606F95;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  max-width: 100%;
  flex: 1 1 0;
}
.listing-badges-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}
.listing-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #F7F8FA;
  border-radius: 6px;
  padding: 4px 10px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #606F95;
  min-height: 24px;
  position: relative;
}

/* Fix tooltip hover interruption by creating invisible overlay covering entire badge area */
.listing-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: inherit;
  pointer-events: none;
  z-index: var(--z-base);
}

/* Ensure badge content is above the overlay but still allows hover events */
.listing-badge > * {
  position: relative;
  z-index: var(--z-surface);
  pointer-events: none;
}
.listing-badge img {
  width: 12px !important;
  height: 12px !important;
  min-width: 12px !important;
  min-height: 12px !important;
  max-width: 12px !important;
  max-height: 12px !important;
  margin-right: 0 !important;
  margin-left: 0 !important;
  vertical-align: middle !important;
}
.listing-badge.fit-types img {
  margin-right: 2px;
  margin-left: 2px;
}
.listing-badge.fit-types {
  gap: 2px;
  background: #F7F8FA;
  border-radius: 6px;
  padding: 4px 10px;
}
.listing-product-type-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 700;
  color: #606F95;
}
.listing-product-type-ic {
  width: 16px;
  height: 16px;
}
.listing-product-type {
  margin-top: 2px;
  font-size: 12px;
  font-weight: 500;
}
.listing-product-price {
  color: #606F95;
  font-weight: 700;
  margin-left: 8px;
  margin-top: 2px;
}

.listing-product-price-only {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.listing-product-price-only .listing-product-price {
  margin-left: 0;
  font-size: 14px;
  font-weight: 700;
}
.listing-ad-row {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #606F95;
  background: linear-gradient(90deg, rgba(96,111,149,0) 0%, rgba(96,111,149,0.10) 100%) !important;
  border: 1.3px solid rgba(96,111,149,0.2) !important;
  border-radius: 6px !important;
  padding: 4px 8px;
}
.listing-ad-row .listing-ad-ic { width: 12px !important; height: 12px !important; }
.listing-ad-row .listing-ad-label { margin-top: 2px !important; display: inline-block; font-weight: 600; }

/* Ad spend status colors */
/* Keep label uncolored; status is conveyed by the icon source updated in JS */
.listing-ad-ic {
  width: 16px;
  height: 16px;
}
.listing-edit-analyse-row {
  display: flex;
  align-items: center;
  gap: 8px;
}
.listing-analyse-ic, .listing-edit-ic {
  position: relative;
  width: 32px;
  height: 32px;
  cursor: pointer;
  border-radius: 50%;
  background: #E9EBF2;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
}

.listing-analyse-ic img {
  width: 16px;
  height: 16px;
  position: relative;
  z-index: var(--z-base);
}

.listing-edit-ic img {
  width: 14px;
  height: 14px;
  position: relative;
  z-index: var(--z-base);
}

.listing-analyse-ic:hover {
  background: #04AE2C;
}

.listing-edit-ic:hover {
  background: #470CED;
}

/* Change icon source on hover using background approach */
.listing-analyse-ic:hover img {
  opacity: 0;
}

.listing-edit-ic:hover img {
  opacity: 0;
}

.listing-analyse-ic:hover::before {
  content: '';
  width: 16px;
  height: 16px;
  background: url('./assets/listing-analyse-hover-ic.svg') center/contain no-repeat;
  position: absolute;
  z-index: var(--z-surface);
}

.listing-edit-ic:hover::before {
  content: '';
  width: 14px;
  height: 14px;
  background: url('./assets/edit-hover-ic.svg') center/contain no-repeat;
  position: absolute;
  z-index: var(--z-surface);
}







/* Dark theme support */
[data-theme="dark"] .listing-analyse-ic,
[data-theme="dark"] .listing-edit-ic {
  background: var(--border-color);
}

[data-theme="dark"] .listing-analyse-ic:hover {
  background: #04AE2C;
}

[data-theme="dark"] .listing-edit-ic:hover {
  background: #470CED;
}
/* Responsive adjustments */
@media (max-width: 1200px) {
  .listing-analytics-div {
    display: grid;
    grid-template-columns: 75px 1fr;
    grid-template-rows: auto auto;
    grid-template-areas:
      "image actions"
      "content content";
    gap: 16px 16px;
    padding: 0;
    align-items: start;
  }

  .listing-left-div {
    grid-area: image;
    align-self: start;
  }

  .listing-right-div {
    grid-area: actions;
    align-items: flex-end;
    min-width: 0;
    max-width: none;
    justify-self: end;
  }

  .listing-middle-div {
    grid-area: content;
    min-width: 0;
    width: 100%;
  }
}
@media (max-width: 800px) {
  .listing-analytics-div {
    display: grid;
    grid-template-columns: 75px 1fr;
    grid-template-rows: auto auto;
    grid-template-areas:
      "image actions"
      "content content";
    gap: 12px 12px;
    padding: 0;
    align-items: start;
  }

  .listing-left-div {
    grid-area: image;
    align-self: start;
  }

  .listing-right-div {
    grid-area: actions;
    align-items: flex-end;
    min-width: 0;
    max-width: none;
    justify-self: end;
  }

  .listing-middle-div {
    grid-area: content;
    min-width: 0;
    width: 100%;
  }

  .listing-title {
    font-size: 14px;
  }
  .listing-badge {
    font-size: 11px;
    padding: 3px 6px;
  }
}
[data-theme="dark"] .listing-analytics-div {
  background: var(--bg-primary);
  border-color: var(--border-color);
}
[data-theme="dark"] .listing-badge,
[data-theme="dark"] .listing-product-type-row,
[data-theme="dark"] .listing-ad-row { color: #B4B9C5; }
/* Match ASIN badge style in dark mode too (same as light) */
[data-theme="dark"] .listing-ad-row {
  background: linear-gradient(90deg, rgba(96,111,149,0) 0%, rgba(96,111,149,0.10) 100%) !important;
  border-color: rgba(96,111,149,0.2) !important;
}

[data-theme="dark"] .listing-title {
  color: #FFFFFF;
}
[data-theme="dark"] .listing-badge {
  background: #292E38;
}
[data-theme="dark"] .listing-product-price {
  color: #ffffff;
}

/* Sidebar: disable transitions on all children for instant theme toggle (preserve width on sidebar itself) */
.sidebar * {
  transition: none !important;
}
/* Listing-left-div states: loading, loaded, private */
.listing-left-div .preloader {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-width: 32px;
  min-height: 40px;
  background: #E9EBF2 url('./assets/snap-loader-ic.svg') center no-repeat;
  background-size: 32px 40px;
  position: relative;
  overflow: hidden;
}

/* Skeleton loader shimmer effect */
.listing-left-div .preloader::before {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 150%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 20%,
    rgba(255, 255, 255, 0.3) 40%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.3) 60%,
    rgba(255, 255, 255, 0.1) 80%,
    transparent 100%
  );
  animation: skeleton-shimmer 1.5s ease-in-out infinite alternate;
}

@keyframes skeleton-shimmer {
  0% {
    left: -150%;
  }
  100% {
    left: 150%;
  }
}
.listing-left-div .listing-product-img,
.listing-left-div .privacy-icon,
.listing-left-div .privacy-text {
  display: none;
}

.listing-left-div.state-private .preloader {
  display: none;
}

.listing-left-div.state-loading .preloader {
  display: flex;
  width: 100%;
  height: 100%;
  background: #E9EBF2 url('./assets/snap-loader-ic.svg') center no-repeat;
  background-size: 32px 40px;
  position: relative;
  overflow: hidden;
}

.listing-left-div.state-loaded .listing-product-img {
  display: block;
}

.listing-left-div.state-private {
  background: #470CED;
}

.listing-left-div.state-private .privacy-icon {
  display: block;
  width: 24px;
  height: 24px;
  position: absolute;
  top: 12px;
  left: 12px;
}

.listing-left-div.state-private .privacy-text {
  display: block;
  position: absolute;
  bottom: 12px;
  left: 12px;
  right: 12px;
  color: #FFFFFF;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.2;
  text-align: left;
}

/* Privacy mode: hide specific badges but keep title visible */
.listing-left-div.state-private ~ .listing-middle-div .BSR-badge,
.listing-left-div.state-private ~ .listing-middle-div .Asin-badge,
.listing-left-div.state-private ~ .listing-middle-div .hot-seller-badge,
.listing-left-div.state-private ~ .listing-middle-div .instant-seller-badge,
.listing-left-div.state-private ~ .listing-middle-div .blazing-seller-badge,
.listing-left-div.state-private ~ .listing-middle-div .top-seller-badge,
.listing-left-div.state-private ~ .listing-middle-div .fresh-seller-badge,
.listing-left-div.state-private ~ .listing-middle-div .sale-badge {
  display: none;
}

.listing-left-div.state-private ~ .listing-middle-div .listing-title {
  color: #606F95;
  font-style: normal;
  font-size: 13px;
  font-size: 0; /* Hide original text */
}

.listing-left-div.state-private ~ .listing-middle-div .listing-title::after {
  content: 'Privacy Mode is enabled.';
  font-size: 13px;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
}

/* Dark theme: make privacy mode listing title white */
[data-theme="dark"] .listing-left-div.state-private ~ .listing-middle-div .listing-title {
  color: #FFFFFF;
}

/* Dev-mode: force loading spinner when no loaded/private state */
body.dev-mode .listing-left-div:not(.state-loaded):not(.state-private) .preloader {
  display: flex !important;
}

/* Dark theme for preloader */
[data-theme="dark"] .listing-left-div .preloader,
[data-theme="dark"] .listing-left-div.state-loading .preloader {
  background: var(--border-color) url('./assets/snap-loader-ic.svg') center no-repeat;
  background-size: 32px 40px;
}

/* Dark theme shimmer effect */
[data-theme="dark"] .listing-left-div .preloader::before {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.03) 20%,
    rgba(255, 255, 255, 0.08) 40%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.08) 60%,
    rgba(255, 255, 255, 0.03) 80%,
    transparent 100%
  );
}

/* Snap Image Studio specific override */
.snap-image-studio-component .tab-container {
  display: inline-flex !important;
  align-self: flex-start !important;
  min-width: 0 !important; /* Override global min-width rule */
  width: auto !important; /* Ensure auto width to fit content */
}

/* Notification override: shrink to fit content */
.main-content > .notification {
  display: inline-block !important;
  width: auto !important;
  box-sizing: border-box !important;
}

.fit-type {
  display: inline-flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 2px !important;
  vertical-align: middle !important;
  margin-top: 0 !important;
}

.fit-type img {
  width: 12px !important;
  height: 12px !important;
  min-width: 12px !important;
  min-height: 12px !important;
  max-width: 12px !important;
  max-height: 12px !important;
  margin: 0 !important;
  vertical-align: middle !important;
  margin-top: 0 !important;
}

.type-number {
  font-size: 12px !important;
  font-weight: 500 !important;
  margin-top: 2px !important;
  line-height: 1 !important;
}

.fit-type > *:not(img):not(.type-number) {
  margin-top: 0 !important;
}

.royalties-badge {
  /* Figma spec: green gradient bg, green border, green text, 6px radius */
  background: linear-gradient(90deg, rgba(4,174,44,0) 0%, rgba(4,174,44,0.10) 100%) !important;
  border: 1.3px solid rgba(4,174,44,0.2) !important;
  color: #04AE2C !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.royalties-badge img {
  /* Ensure icon uses Figma green */
  filter: none !important;
  color: #04AE2C !important;
  fill: #04AE2C !important;
}
[data-theme="dark"] .royalties-badge {
  /* Keep same green, but tweak bg for dark if needed */
  background: linear-gradient(90deg, rgba(4,174,44,0) 0%, rgba(4,174,44,0.18) 100%) !important;
  border: 1.3px solid rgba(4,174,44,0.3) !important;
  color: #04AE2C !important;
}
[data-theme="dark"] .royalties-badge img {
  color: #04AE2C !important;
  fill: #04AE2C !important;
  filter: none !important;
}

.listing-badge, .royalties-badge {
  height: 24px !important;
  min-height: 24px !important;
  max-height: 24px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  padding-left: 8px !important;
  padding-right: 8px !important;
  line-height: 1 !important;
  align-items: center !important;
  position: relative;
}

/* Apply same hover fix to royalties badges */
.royalties-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: inherit;
  pointer-events: none;
  z-index: var(--z-base);
}

.royalties-badge > * {
  position: relative;
  z-index: var(--z-surface);
  pointer-events: none;
}

.listing-badge img, .royalties-badge img {
  width: 12px !important;
  height: 12px !important;
  min-width: 12px !important;
  min-height: 12px !important;
  max-width: 12px !important;
  max-height: 12px !important;
  margin-right: 0 !important;
  margin-left: 0 !important;
  vertical-align: middle !important;
}

.listing-badge span, .royalties-badge span {
  margin-top: 2px !important;
  display: inline-block;
}

/* Override global badge span margin for fit-types badge */
.listing-badge.fit-types-badge .fit-type {
  margin-top: 0 !important;
}
.listing-badge.fit-types-badge .fit-type img {
  margin-top: 0 !important;
}

/* --- BADGE GRADIENTS & STROKES (Figma accurate) --- */

/* Lost Royalties Badge */
.lost-royalties-badge {
  background: linear-gradient(90deg, rgba(255,57,31,0) 0%, rgba(255,57,31,0.10) 100%) !important;
  border: 1.3px solid rgba(255,57,31,0.2) !important;
  color: #FF391F !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.lost-royalties-badge img {
  color: #FF391F !important;
  fill: #FF391F !important;
  filter: none !important;
}

/* Order Units Badge */
.order-units-badge {
  background: linear-gradient(90deg, rgba(4,174,44,0) 0%, rgba(4,174,44,0.10) 100%) !important;
  border: 1.3px solid rgba(4,174,44,0.2) !important;
  color: #04AE2C !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.order-units-badge img {
  color: #04AE2C !important;
  fill: #04AE2C !important;
  filter: none !important;
}

/* Returned Units Badge */
.returned-units-badge {
  background: linear-gradient(90deg, rgba(255,57,31,0) 0%, rgba(255,57,31,0.10) 100%) !important;
  border: 1.3px solid rgba(255,57,31,0.2) !important;
  color: #FF391F !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.returned-units-badge img {
  color: #FF391F !important;
  fill: #FF391F !important;
  filter: none !important;
}

/* Canceled Units Badge */
.canceled-units-badge {
  background: linear-gradient(90deg, rgba(253,195,0,0) 0%, rgba(253,195,0,0.10) 100%) !important;
  border: 1.3px solid rgba(253,195,0,0.2) !important;
  color: #FDC300 !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.canceled-units-badge img {
  color: #FDC300 !important;
  fill: #FDC300 !important;
  filter: none !important;
}

/* Ordered Colors Badge - Special styling with color circles */
.ordered-colors-badge {
  background: linear-gradient(90deg, rgba(96,111,149,0) 0%, rgba(96,111,149,0.10) 100%) !important;
  border: 1.3px solid rgba(96,111,149,0.2) !important;
  color: #606F95 !important;
  border-radius: 6px !important;
  font-weight: 500;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}
.ordered-colors-badge img {
  color: #606F95 !important;
  fill: #606F95 !important;
  filter: none !important;
}
.color-circles {
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
  margin-top: 0 !important;
}
.color-item {
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
  margin-top: 0 !important;
}
.color-circle {
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  display: inline-block !important;
  border: 0.5px solid rgba(96,111,149,0.2) !important;
  flex-shrink: 0 !important;
  margin-top: 0 !important;
}
.color-number {
  font-size: 12px !important;
  font-weight: 500 !important;
  color: #606F95 !important;
  margin-top: 2px !important;
  line-height: 1 !important;
}

/* Override global badge span margin for ordered colors badge (except color-number) */
.ordered-colors-badge .color-circles,
.ordered-colors-badge .color-item,
.ordered-colors-badge .color-circle {
  margin-top: 0 !important;
}

/* Dark theme support for ordered colors badge */
[data-theme="dark"] .ordered-colors-badge {
  background: linear-gradient(90deg, rgba(96,111,149,0) 0%, rgba(96,111,149,0.18) 100%) !important;
  border: 1.3px solid rgba(96,111,149,0.3) !important;
  color: #B4B9C5 !important;
}
[data-theme="dark"] .ordered-colors-badge img {
  color: #B4B9C5 !important;
  fill: #B4B9C5 !important;
}
[data-theme="dark"] .color-circle {
  border: 0.5px solid rgba(180,185,197,0.3) !important;
}
[data-theme="dark"] .color-number {
  color: #B4B9C5 !important;
}

/* Ordered Colors Custom Tooltip */
.ordered-colors-tooltip {
  position: absolute;
  left: 50%;
  bottom: calc(100% + 8px);
  transform: translateX(-50%);
  background: var(--tooltip-bg, #000000) !important;
  color: var(--tooltip-text, #FFFFFF) !important;
  border-radius: 10px;
  padding: 12px;
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: var(--tooltip-font-size, 12px);
  font-weight: 500;
  z-index: var(--z-modal);
  min-width: 120px;
  text-align: left;
  line-height: 1.5;
  pointer-events: none;
  display: none;
  white-space: normal;
}
.ordered-colors-tooltip-title {
  font-weight: 700;
  font-size: var(--tooltip-font-size, 12px);
  margin-bottom: 8px;
  color: var(--tooltip-text, #FFFFFF);
}
.ordered-colors-tooltip-row {
  font-size: var(--tooltip-font-size, 12px);
  font-weight: 500;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  color: var(--tooltip-text, #FFFFFF);
}
.ordered-colors-tooltip-row:last-child {
  margin-bottom: 0;
}
.ordered-colors-tooltip-arrow {
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: var(--tooltip-arrow-size, 5px) solid transparent;
  border-right: var(--tooltip-arrow-size, 5px) solid transparent;
  border-top: var(--tooltip-arrow-size, 5px) solid var(--tooltip-bg, #000000);
}
.listing-badge.ordered-colors-badge {
  position: relative;
}

/* Fit Types, ASIN, Published Date, Amazon Choice, Total Sold/Returned */
.fit-types-badge,
.Asin-badge,
.published-date-badge,
.amazon-choice-badge,
.total-sold-returned-badge {
  background: linear-gradient(90deg, rgba(96,111,149,0) 0%, rgba(96,111,149,0.10) 100%) !important;
  border: 1.3px solid rgba(96,111,149,0.2) !important;
  color: #606F95 !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.fit-types-badge img,
.Asin-badge img,
.published-date-badge img,
.amazon-choice-badge img,
.total-sold-returned-badge img {
  color: #606F95 !important;
  fill: #606F95 !important;
  filter: none !important;
}

/* Amazon Choice Badge - Black icon in light mode only */
:root .amazon-choice-badge img {
  color: #000000 !important;
  fill: #000000 !important;
  filter: brightness(0) !important;
}

/* Amazon Choice Badge - Keep gray icon in dark mode */
[data-theme="dark"] .amazon-choice-badge img {
  color: #606F95 !important;
  fill: #606F95 !important;
  filter: none !important;
}

/* BSR Badge */
.BSR-badge {
  background: #470CED !important;
  color: #fff !important;
  border-radius: 6px !important;
  font-weight: 500;
  border: none !important;
}

/* Last Month Sold Units Badge */
.last-month-sold-units-badge {
  background: #6F00FF !important;
  color: #fff !important;
  border-radius: 6px !important;
  font-weight: 500;
  border: none !important;
}

/* Hot Seller, Rating Badge */
.hot-seller-badge,
.Rating-badge {
  background: linear-gradient(90deg, rgba(253,89,0,0) 0%, rgba(253,89,0,0.10) 100%) !important;
  border: 1.3px solid rgba(253,89,0,0.2) !important;
  color: #FD5900 !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.hot-seller-badge img,
.Rating-badge img {
  color: #FD5900 !important;
  fill: #FD5900 !important;
  filter: none !important;
}

/* Sale Badge, Top Seller Badge */
.sale-badge,
.top-seller-badge {
  background: linear-gradient(90deg, rgba(255,57,31,0) 0%, rgba(255,57,31,0.10) 100%) !important;
  border: 1.3px solid rgba(255,57,31,0.2) !important;
  color: #FF391F !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.sale-badge img,
.top-seller-badge img {
  color: #FF391F !important;
  fill: #FF391F !important;
  filter: none !important;
}

/* Instant Seller Badge */
.instant-seller-badge {
  background: linear-gradient(90deg, rgba(255,149,0,0) 0%, rgba(255,149,0,0.10) 100%) !important;
  border: 1.3px solid rgba(255,149,0,0.2) !important;
  color: #FF9500 !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.instant-seller-badge img {
  color: #FF9500 !important;
  fill: #FF9500 !important;
  filter: none !important;
}

/* Blazing Seller Badge */
.blazing-seller-badge {
  background: linear-gradient(90deg, rgba(4,174,44,0) 0%, rgba(4,174,44,0.10) 100%) !important;
  border: 1.3px solid rgba(4,174,44,0.2) !important;
  color: #04AE2C !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.blazing-seller-badge img {
  color: #04AE2C !important;
  fill: #04AE2C !important;
  filter: none !important;
}

/* Fresh Seller Badge */
.fresh-seller-badge {
  background: linear-gradient(90deg, rgba(15,188,249,0) 0%, rgba(15,188,249,0.10) 100%) !important;
  border: 1.3px solid rgba(15,188,249,0.2) !important;
  color: #0FBCF9 !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.fresh-seller-badge img {
  color: #0FBCF9 !important;
  fill: #0FBCF9 !important;
  filter: none !important;
}

/* New Seller Listing Identification Styles */
.listing-analytics-div[data-new-seller="true"],
.listing-analytics-div.new-seller-listing {
  /* Subtle visual indicator for New Seller listings (optional) */
  position: relative;
}

/* Optional: Add a subtle border or glow to New Seller listings for visual identification */
.listing-analytics-div[data-new-seller="true"]::before,
.listing-analytics-div.new-seller-listing::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 8px;
  background: linear-gradient(90deg, rgba(15,188,249,0.05) 0%, rgba(15,188,249,0.02) 100%);
  pointer-events: none;
  z-index: calc(var(--z-base) * -1);
}

/* Dark theme support for New Seller listing identification */
[data-theme="dark"] .listing-analytics-div[data-new-seller="true"]::before,
[data-theme="dark"] .listing-analytics-div.new-seller-listing::before {
  background: linear-gradient(90deg, rgba(15,188,249,0.08) 0%, rgba(15,188,249,0.03) 100%);
}

/* Last Sold Badge */
.last-sold-badge {
  background: linear-gradient(90deg, rgba(0,171,190,0) 0%, rgba(0,171,190,0.10) 100%) !important;
  border: 1.3px solid rgba(0,171,190,0.2) !important;
  color: #00ABBE !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.last-sold-badge img {
  color: #00ABBE !important;
  fill: #00ABBE !important;
  filter: none !important;
}

/* Sold Time Badge, Competing Listings Badge */
.sold-time-badge,
.competing-lisitngs-badge {
  background: linear-gradient(90deg, rgba(4,174,44,0) 0%, rgba(4,174,44,0.10) 100%) !important;
  border: 1.3px solid rgba(4,174,44,0.2) !important;
  color: #04AE2C !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.sold-time-badge img,
.competing-lisitngs-badge img {
  color: #04AE2C !important;
  fill: #04AE2C !important;
  filter: none !important;
}

/* --- END BADGE GRADIENTS --- */

.competing-lisitngs-badge {
  background: #04AE2C !important;
  color: #fff !important;
  border-radius: 6px !important;
  font-weight: 500;
  border: none !important;
  height: 24px !important;
  min-height: 24px !important;
  max-height: 24px !important;
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
  padding: 0 10px !important;
}
.competing-lisitngs-badge img {
  color: #fff !important;
  fill: #fff !important;
  filter: none !important;
  width: 14px !important;
  height: 12px !important;
  min-width: 14px !important;
  min-height: 12px !important;
  max-width: 14px !important;
  max-height: 12px !important;
  margin-right: 0 !important;
  margin-left: 0 !important;
  vertical-align: middle !important;
}

/* Hide canceled-units-badge when value is 0 */
.canceled-units-badge.has-zero-value {
  display: none !important;
}

/* Hide returned-units-badge when value is 0 or -0 */
.returned-units-badge.has-zero-value {
  display: none !important;
}

/* Alternative approach using CSS attribute selectors for better browser support */
.canceled-units-badge span:last-child[data-value="0"],
.returned-units-badge span:last-child[data-value="0"],
.returned-units-badge span:last-child[data-value="-0"] {
  display: none !important;
}

/* :has() selectors with JavaScript fallbacks for browser compatibility */
.canceled-units-badge:has(span:last-child[data-value="0"]),
.returned-units-badge:has(span:last-child[data-value="0"]),
.returned-units-badge:has(span:last-child[data-value="-0"]) {
  display: none !important;
}

/* JavaScript fallback classes for browsers without :has() support */
.canceled-units-badge.has-zero-value,
.returned-units-badge.has-zero-value {
  display: none !important;
}

/* Search No Results State */
.search-no-results {
  width: 100%;
  display: none;
  box-sizing: border-box;
  /* Center within available space below marketplaces */
  flex: 1; /* Take up remaining vertical space */
  min-height: 200px; /* Minimum height to ensure visibility */
  margin-top: 24px; /* Space from marketplaces-sales-row */
  padding: 0; /* Remove fixed padding */
}

/* Hide listing dividers when no results state is active */
.search-no-results:not([style*="display: none"]) ~ .listing-section-divider,
.search-no-results[style*="display: block"] ~ .listing-section-divider {
  display: none !important;
}

.no-results-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  width: 100%;
  height: 100%; /* Fill the parent container */
}

.no-results-image {
  width: 104px; /* 200% of original 52px */
  height: 104px; /* 200% of original 52px */
}

.no-results-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.no-results-message {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.2;
  color: var(--text-primary);
  text-align: center;
}

.search-term-display {
  font-weight: 700;
  color: var(--text-accent);
}

/* Dark theme support for no results */
[data-theme="dark"] .no-results-message {
  color: var(--text-primary);
}

[data-theme="dark"] .search-term-display {
  color: var(--text-accent);
}

/* Duplicate Sales Section Divider rule removed - see line 3357 for the main rule */

/* Listing Section Divider */
.listing-section-divider {
  width: 100%;
  height: 1.5px;
  background: rgba(232, 235, 244, 0.5);
  border: none;
  margin: 24px 0 0 0;
  flex-shrink: 0;
  /* Smooth transitions for divider visibility changes */
  transition: opacity 0.15s ease-in-out;
}
[data-theme="dark"] .listing-section-divider {
  background: rgba(255, 255, 255, 0.05);
}

/* No Sales State Styles (Figma accurate) */
.no-sales-state {
  display: none; /* Initially hidden */
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
  /* Center within available space below marketplaces */
  flex: 1; /* Take up remaining vertical space */
  min-height: 200px; /* Minimum height to ensure visibility */
  margin-top: 24px; /* Space from marketplaces-sales-row */
  padding: 0; /* Remove fixed padding */
}

.no-reviews-state {
  display: none;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
  flex: 1;
  min-height: 200px;
  padding: 0;
}

.no-sales-img {
  width: 98.18px;
  height: 108.66px;
  flex-shrink: 0;
}

.no-reviews-img {
  width: 98.18px;
  height: 108.66px;
  flex-shrink: 0;
}
.no-sales-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
}

.no-reviews-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
}

.no-sales-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 1.197;
  color: var(--text-primary);
  margin: 0;
}

.no-reviews-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 1.197;
  color: var(--text-primary);
  margin: 0;
}

.no-sales-subtitle {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.197;
  color: var(--text-primary);
  margin: 0;
}

.no-reviews-subtitle {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.197;
  color: var(--text-primary);
  margin: 0;
}

/* Dark theme support for no-sales state */
[data-theme="dark"] .no-sales-title,
[data-theme="dark"] .no-sales-subtitle {
  color: var(--text-primary);
}

/* Dark theme support for no-reviews state */
[data-theme="dark"] .no-reviews-title,
[data-theme="dark"] .no-reviews-subtitle {
  color: var(--text-primary);
}

/* Scoped: ensure reviews card empty state height matches icon height exactly */
.insights-and-feedback-section .customer-reviews-card-div .no-reviews-state {
  flex: 0 0 auto;
  min-height: 0;
  height: auto;
  margin-top: auto;
  margin-bottom: auto;
}

/* No payout empty states */
.no-payout-state,
.no-payout-marketplace {
  display: none;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
  flex: 1;
  min-height: 200px;
  padding: 0;
}

.no-payout-img {
  width: 98.18px;
  height: 108.66px;
  flex-shrink: 0;
}

.no-payout-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
}

.no-payout-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 20px;
  color: var(--text-primary);
  margin: 0;
}

.no-payout-subtitle {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  color: var(--text-secondary);
  margin: 0;
}

/* Dark theme support for no-payout state */
[data-theme="dark"] .no-payout-title,
[data-theme="dark"] .no-payout-subtitle {
  color: var(--text-primary);
}

/* ============================================================================
   LAST WEEK'S SALES CARD STYLES
   ============================================================================ */

/* Last Week's Sales Card - Full Width */
.last-week-sales-card,
.monthly-sales-card,
.yearly-sales-card {
  width: 100%;
  background: var(--bg-primary);
  border-radius: 14px;
  padding: 24px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  margin-top: 16px;
  overflow: hidden; /* prevent any child content from overflowing */
}

[data-theme="dark"] .last-week-sales-card,
[data-theme="dark"] .monthly-sales-card,
[data-theme="dark"] .yearly-sales-card {
  background: var(--bg-primary);
}

/* Today vs Previous Years Sales Card - Full Width */
.today-vs-previous-years-card {
  width: 100%;
  background: var(--bg-primary);
  border-radius: 14px;
  padding: 24px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  margin-top: 16px;
}

[data-theme="dark"] .today-vs-previous-years-card {
  background: var(--bg-primary);
}

/* Header Layout - Title/Date on left, Controls on right */
.last-week-sales-card .Sales-title-date-div,
.monthly-sales-card .Sales-title-date-div,
.yearly-sales-card .Sales-title-date-div,
.today-vs-previous-years-card .Sales-title-date-div {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0px;
  width: 100%;
}

.title-date-section {
  display: flex;
  align-items: center;
  gap: 10px; /* Exactly 10px gap between title and date */
  flex: 1;
  min-width: 0;
  /* Ensure proper spacing: [Icon] [Title] [Date] [Flexible Gap] */
}

.title-date-text {
  display: flex;
  align-items: center;
  gap: 10px;
}

.last-week-sales-card .controls-section,
.monthly-sales-card .controls-section,
.today-vs-previous-years-card .controls-section,
.yearly-sales-card .controls-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Quarterly Sales Summary */
.monthly-sales-card #quarter-sales-div {
  margin-top: 8px; /* Below title/date row */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden; /* constrain quarter-grid within this container */
}

.quarter-grid {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start; /* maintain left alignment */
  gap: 16px; /* default gap - equal spacing between cards */
  width: auto; /* size to content, not full width */
  max-width: 100%; /* but never exceed container */
  margin-top: 16px;
  flex-wrap: nowrap;
  box-sizing: border-box;
  overflow: visible; /* allow content to be visible but constrained by parent */
}

/* Responsive gap adjustments for tighter widths */
@media (max-width: 1400px) {
  .quarter-grid {
    gap: 12px; /* reduce gap when space gets tight */
  }
}

@media (max-width: 1200px) {
  .quarter-grid {
    gap: 8px; /* further reduce gap for smaller screens */
  }
}

@media (max-width: 1024px) {
  .quarter-grid {
    gap: 6px; /* minimal gap for very tight spaces */
  }
}

.quarter-card {
  display: flex;
  flex-direction: column;
  gap: 10px; /* inner gap within card */
  flex: 0 1 auto; /* don't grow, can shrink, size based on content */
  min-width: 0; /* allow shrinking below content size */
  max-width: 100%;
  box-sizing: border-box;
}

.quarter-divider {
  width: 1px;
  background-color: var(--border-color);
  align-self: stretch; /* stretch to match the height of quarter-cards */
  margin: 0 16px; /* 16px margin on each side for total 32px spacing */
}

.quarter-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.197;
  color: var(--text-primary);
  display: inline-flex; /* ensure children align vertically */
  align-items: center;  /* vertical centering */
}

.quarter-label .quarter-compare { display: inline-flex; align-items: center; justify-content: center; gap: 6px; margin-left: 8px; line-height: 1; vertical-align: middle; padding: 2px 4px; }
/* Keep tooltip open across the entire compare block (icon, gap, and text) */
.quarter-label .quarter-compare * { pointer-events: none; }
.quarter-label .comparison-percentage { font-weight: 700; font-size: 12px; }
.quarter-label .comparison-percentage.positive { color: #04AE2C; }
.quarter-label .comparison-percentage.negative { color: #FF391F; }
.quarter-label .comparison-percentage.neutral { color: #606F95; }

.quarter-table {
  display: grid;
  grid-template-columns: max-content max-content max-content; /* columns size to their content */
  column-gap: 16px; /* default inner horizontal gap */
  row-gap: 4px;      /* inner vertical gap */
  align-items: baseline;
  white-space: nowrap; /* keep each cell on one line */
  width: 100%;
  box-sizing: border-box;
}

/* Responsive column gap adjustments for quarter-table */
@media (max-width: 1400px) {
  .quarter-table {
    column-gap: 12px; /* reduce inner gap when space gets tight */
  }
}

@media (max-width: 1200px) {
  .quarter-table {
    column-gap: 10px; /* further reduce inner gap for smaller screens */
  }
}

@media (max-width: 1024px) {
  .quarter-table {
    column-gap: 8px; /* minimal inner gap for very tight spaces */
  }
}
.quarter-row { display: contents; } /* allow cells to align in the grid columns */
.quarter-row.header { opacity: 0.85; }
.quarter-cell { display: inline-flex; align-items: center; gap: 6px; }
.header-cell { font-family: 'Amazon Ember', sans-serif; font-weight: 500; font-size: 11px; color: #606F95; }
.quarter-cell.returns .returns-number { font-family: 'Amazon Ember', sans-serif; font-weight: 700; font-size: 14px; color: var(--text-primary); }
.value-cell { font-family: 'Amazon Ember', sans-serif; font-weight: 700; font-size: 14px; color: var(--text-primary); }
.quarter-cell.returns .returns-rate { margin-left: 0px; font-weight: 700; font-size: 12px; color: #FF391F; }
.quarter-cell.returns.zero .returns-rate { color: var(--text-primary); opacity: 0.4; }
.quarter-cell.returns.zero .returns-number { color: var(--text-primary); opacity: 0.4; }

.quarter-metrics .metric {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.metric-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 11px;
  line-height: 1.197;
  color: #606F95;
}

.metric-value {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  line-height: 1.197;
  color: var(--text-primary);
}

.metric-value.zero {
  color: #000;
  opacity: 0.4;
}

[data-theme="dark"] .metric-value.zero {
  color: #fff;
  opacity: 0.4;
}

/* Generic zero-value styling for quarterly cells */
.quarter-table .value-cell.zero { color: var(--text-primary); opacity: 0.4; }
.quarter-table .returns.zero .returns-number, .quarter-table .returns.zero .returns-rate { color: var(--text-primary); opacity: 0.4; }

/* Dark theme: make header labels white */
[data-theme="dark"] .quarter-row.header .header-cell { color: #ffffff; }

/* Dark theme for quarter divider */
[data-theme="dark"] .quarter-divider {
  background-color: #2A2D35;
}

.hidden { display: none !important; }

/* Show/Hide Options Button */
.last-week-sales-card .show-hide-options-div,
.monthly-sales-card .show-hide-options-div,
.today-vs-previous-years-card .show-hide-options-div,
.yearly-sales-card .show-hide-options-div {
  display: flex;
  align-items: center;
  position: relative; /* For dropdown positioning */
}

.last-week-sales-card .show-hide-options-btn,
.monthly-sales-card .show-hide-options-btn,
.today-vs-previous-years-card .show-hide-options-btn,
.yearly-sales-card .show-hide-options-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #F7F8FA;
  border-radius: 4px;
  cursor: pointer;
  box-sizing: border-box;
}

.last-week-sales-card .show-hide-options-btn:hover,
.monthly-sales-card .show-hide-options-btn:hover,
.today-vs-previous-years-card .show-hide-options-btn:hover,
.yearly-sales-card .show-hide-options-btn:hover {
  background: #E5E7EB;
}

/* Show/Hide Options button active state (when dropdown is open) */
.last-week-sales-card .show-hide-options-btn.active,
.monthly-sales-card .show-hide-options-btn.active,
.today-vs-previous-years-card .show-hide-options-btn.active,
.yearly-sales-card .show-hide-options-btn.active {
  background: #470CED !important;
}

.last-week-sales-card .show-hide-options-btn.active img,
.monthly-sales-card .show-hide-options-btn.active img,
.today-vs-previous-years-card .show-hide-options-btn.active img,
.yearly-sales-card .show-hide-options-btn.active img {
  filter: brightness(0) invert(1); /* Make icon white */
}

[data-theme="dark"] .last-week-sales-card .show-hide-options-btn,
[data-theme="dark"] .monthly-sales-card .show-hide-options-btn,
[data-theme="dark"] .today-vs-previous-years-card .show-hide-options-btn,
[data-theme="dark"] .yearly-sales-card .show-hide-options-btn {
  background: #292E38; /* Match listing-edit-ic and sales-filter-div background */
}

[data-theme="dark"] .last-week-sales-card .show-hide-options-btn:hover,
[data-theme="dark"] .monthly-sales-card .show-hide-options-btn:hover,
[data-theme="dark"] .today-vs-previous-years-card .show-hide-options-btn:hover,
[data-theme="dark"] .yearly-sales-card .show-hide-options-btn:hover {
  background: #3A4048;
}

/* Compare Button */
.last-week-sales-card .compare-div,
.monthly-sales-card .compare-div {
  display: flex;
  align-items: center;
  position: relative; /* For dropdown positioning */
}

.last-week-sales-card .compare-btn,
.monthly-sales-card .compare-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #F7F8FA;
  border-radius: 4px;
  cursor: pointer;
  box-sizing: border-box;
}

.last-week-sales-card .compare-btn:hover,
.monthly-sales-card .compare-btn:hover {
  background: #E5E7EB;
}

/* Compare button active state (when compare mode is enabled) */
.last-week-sales-card .compare-btn.active,
.monthly-sales-card .compare-btn.active {
  background: #470CED !important;
}

.last-week-sales-card .compare-btn.active img,
.monthly-sales-card .compare-btn.active img {
  filter: brightness(0) invert(1); /* Make icon white */
}

[data-theme="dark"] .last-week-sales-card .compare-btn,
[data-theme="dark"] .monthly-sales-card .compare-btn {
  background: #292E38; /* Match listing-edit-ic and sales-filter-div background */
}

[data-theme="dark"] .last-week-sales-card .compare-btn:hover,
[data-theme="dark"] .monthly-sales-card .compare-btn:hover {
  background: #3A4048;
}

/* Show/Hide Options Dropdown Menu - Matching compare dropdown styling */
.last-week-sales-card .show-hide-options-dropdown,
.monthly-sales-card .show-hide-options-dropdown,
.today-vs-previous-years-card .show-hide-options-dropdown,
.yearly-sales-card .show-hide-options-dropdown {
  position: absolute;
  top: calc(100% + 10px); /* 4px gap below the button - matches marketplace dropdown */
  right: 0; /* Right-align with the button */
  width: auto; /* Auto-fit content */
  min-width: 120px; /* Minimum width for "Show Royalties" text */
  background: #FFFFFF;
  border: 1.5px solid #E9EBF2; /* Match marketplace dropdown border */
  border-radius: 8px; /* Match marketplace dropdown border radius */
  z-index: var(--z-dropdown); /* Standardized z-index for dropdown */
  display: none; /* Hidden by default */
  box-sizing: border-box;
  opacity: 0;
  transform: translateY(-10px); /* Only vertical transform for animation */
  transition: opacity 0.2s ease, transform 0.2s ease;
  will-change: transform, opacity;
}

[data-theme="dark"] .last-week-sales-card .show-hide-options-dropdown,
[data-theme="dark"] .monthly-sales-card .show-hide-options-dropdown,
[data-theme="dark"] .today-vs-previous-years-card .show-hide-options-dropdown,
[data-theme="dark"] .yearly-sales-card .show-hide-options-dropdown {
  background: var(--bg-primary); /* Match marketplace dropdown dark background */
  border: 1.5px solid var(--border-color) !important; /* Match marketplace dropdown dark border */
}

.last-week-sales-card .show-hide-options-dropdown.show,
.monthly-sales-card .show-hide-options-dropdown.show,
.today-vs-previous-years-card .show-hide-options-dropdown.show,
.yearly-sales-card .show-hide-options-dropdown.show {
  display: block;
  opacity: 1;
  transform: translateY(0); /* Only vertical transform for animation */
}

.last-week-sales-card .show-hide-options-dropdown-item,
.monthly-sales-card .show-hide-options-dropdown-item,
.today-vs-previous-years-card .show-hide-options-dropdown-item,
.yearly-sales-card .show-hide-options-dropdown-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px; /* Match marketplace dropdown item padding */
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap; /* Prevent text wrapping */
  font-size: 12px; /* Match marketplace dropdown font size */
}

.last-week-sales-card .show-hide-options-dropdown-item:hover,
.monthly-sales-card .show-hide-options-dropdown-item:hover,
.today-vs-previous-years-card .show-hide-options-dropdown-item:hover,
.yearly-sales-card .show-hide-options-dropdown-item:hover {
  background: #F3F4F6; /* Match marketplace dropdown hover color */
}

.last-week-sales-card .show-hide-options-dropdown-item:first-child:hover,
.monthly-sales-card .show-hide-options-dropdown-item:first-child:hover,
.today-vs-previous-years-card .show-hide-options-dropdown-item:first-child:hover,
.yearly-sales-card .show-hide-options-dropdown-item:first-child:hover {
  border-top-left-radius: 5px; /* Match marketplace dropdown corner radius */
  border-top-right-radius: 5px;
}

.last-week-sales-card .show-hide-options-dropdown-item:last-child:hover,
.monthly-sales-card .show-hide-options-dropdown-item:last-child:hover,
.today-vs-previous-years-card .show-hide-options-dropdown-item:last-child:hover,
.yearly-sales-card .show-hide-options-dropdown-item:last-child:hover {
  border-bottom-left-radius: 5px; /* Match marketplace dropdown corner radius */
  border-bottom-right-radius: 5px;
}

[data-theme="dark"] .last-week-sales-card .show-hide-options-dropdown-item:hover,
[data-theme="dark"] .monthly-sales-card .show-hide-options-dropdown-item:hover,
[data-theme="dark"] .today-vs-previous-years-card .show-hide-options-dropdown-item:hover,
[data-theme="dark"] .yearly-sales-card .show-hide-options-dropdown-item:hover {
  background: #292E38 !important; /* Match marketplace dropdown dark hover */
  color: var(--text-accent) !important;
}

[data-theme="dark"] .last-week-sales-card .show-hide-options-dropdown-item:first-child:hover,
[data-theme="dark"] .monthly-sales-card .show-hide-options-dropdown-item:first-child:hover,
[data-theme="dark"] .today-vs-previous-years-card .show-hide-options-dropdown-item:first-child:hover,
[data-theme="dark"] .yearly-sales-card .show-hide-options-dropdown-item:first-child:hover {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

[data-theme="dark"] .last-week-sales-card .show-hide-options-dropdown-item:last-child:hover,
[data-theme="dark"] .monthly-sales-card .show-hide-options-dropdown-item:last-child:hover,
[data-theme="dark"] .today-vs-previous-years-card .show-hide-options-dropdown-item:last-child:hover,
[data-theme="dark"] .yearly-sales-card .show-hide-options-dropdown-item:last-child:hover {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.last-week-sales-card .show-hide-options-checkbox,
.monthly-sales-card .show-hide-options-checkbox,
.today-vs-previous-years-card .show-hide-options-checkbox,
.yearly-sales-card .show-hide-options-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px; /* Increased from 14px to 20px */
  height: 20px; /* Increased from 14px to 20px */
  flex-shrink: 0;
}

.last-week-sales-card .show-hide-options-checkbox img,
.monthly-sales-card .show-hide-options-checkbox img,
.today-vs-previous-years-card .show-hide-options-checkbox img,
.yearly-sales-card .show-hide-options-checkbox img {
  width: 20px; /* Increased from 14px to 20px */
  height: 20px; /* Increased from 14px to 20px */
  transition: opacity 0.2s ease;
}

.last-week-sales-card .show-hide-options-dropdown-text,
.monthly-sales-card .show-hide-options-dropdown-text,
.today-vs-previous-years-card .show-hide-options-dropdown-text,
.yearly-sales-card .show-hide-options-dropdown-text {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px; /* Match marketplace dropdown font size */
  line-height: 1.2;
  color: #606F95;
  flex: 1;
}

[data-theme="dark"] .last-week-sales-card .show-hide-options-dropdown-text,
[data-theme="dark"] .monthly-sales-card .show-hide-options-dropdown-text,
[data-theme="dark"] .today-vs-previous-years-card .show-hide-options-dropdown-text,
[data-theme="dark"] .yearly-sales-card .show-hide-options-dropdown-text {
  color: var(--text-primary) !important; /* Match marketplace dropdown dark text color */
}

/* Compare Dropdown Menu - Matching marketplace dropdown styling */
.last-week-sales-card .compare-dropdown,
.monthly-sales-card .compare-dropdown {
  position: absolute;
  top: calc(100% + 10px); /* 4px gap below the button - matches marketplace dropdown */
  right: 0; /* Right-align with the button */
  width: auto; /* Auto-fit content */
  min-width: 60px; /* Minimum width */
  background: #FFFFFF;
  border: 1.5px solid #E9EBF2; /* Match marketplace dropdown border */
  border-radius: 8px; /* Match marketplace dropdown border radius */
  z-index: var(--z-dropdown); /* Standardized z-index for dropdown */
  display: none; /* Hidden by default */
  box-sizing: border-box;
  opacity: 0;
  transform: translateY(-10px); /* Only vertical transform for animation */
  transition: opacity 0.2s ease, transform 0.2s ease;
  will-change: transform, opacity;
}

[data-theme="dark"] .last-week-sales-card .compare-dropdown,
[data-theme="dark"] .monthly-sales-card .compare-dropdown {
  background: var(--bg-primary); /* Match marketplace dropdown dark background */
  border: 1.5px solid var(--border-color) !important; /* Match marketplace dropdown dark border */
}

.last-week-sales-card .compare-dropdown.show,
.monthly-sales-card .compare-dropdown.show {
  display: block;
  opacity: 1;
  transform: translateY(0); /* Only vertical transform for animation */
}

.last-week-sales-card .compare-dropdown-item,
.monthly-sales-card .compare-dropdown-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px; /* Match marketplace dropdown item padding */
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap; /* Prevent text wrapping */
  font-size: 12px; /* Match marketplace dropdown font size */
}

.last-week-sales-card .compare-dropdown-item:hover,
.monthly-sales-card .compare-dropdown-item:hover {
  background: #F3F4F6; /* Match marketplace dropdown hover color */
}

.last-week-sales-card .compare-dropdown-item:first-child:hover,
.monthly-sales-card .compare-dropdown-item:first-child:hover {
  border-top-left-radius: 5px; /* Match marketplace dropdown corner radius */
  border-top-right-radius: 5px;
}

.last-week-sales-card .compare-dropdown-item:last-child:hover,
.monthly-sales-card .compare-dropdown-item:last-child:hover {
  border-bottom-left-radius: 5px; /* Match marketplace dropdown corner radius */
  border-bottom-right-radius: 5px;
}

[data-theme="dark"] .last-week-sales-card .compare-dropdown-item:hover,
[data-theme="dark"] .last-week-sales-card .compare-dropdown-item.selected,
[data-theme="dark"] .monthly-sales-card .compare-dropdown-item:hover,
[data-theme="dark"] .monthly-sales-card .compare-dropdown-item.selected {
  background: #292E38 !important; /* Match marketplace dropdown dark hover */
  color: var(--text-accent) !important;
}

[data-theme="dark"] .last-week-sales-card .compare-dropdown-item:first-child:hover,
[data-theme="dark"] .last-week-sales-card .compare-dropdown-item:first-child.selected,
[data-theme="dark"] .monthly-sales-card .compare-dropdown-item:first-child:hover,
[data-theme="dark"] .monthly-sales-card .compare-dropdown-item:first-child.selected {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

[data-theme="dark"] .last-week-sales-card .compare-dropdown-item:last-child:hover,
[data-theme="dark"] .last-week-sales-card .compare-dropdown-item:last-child.selected,
[data-theme="dark"] .monthly-sales-card .compare-dropdown-item:last-child:hover,
[data-theme="dark"] .monthly-sales-card .compare-dropdown-item:last-child.selected {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.last-week-sales-card .compare-checkbox,
.monthly-sales-card .compare-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px; /* Increased from 14px to 20px */
  height: 20px; /* Increased from 14px to 20px */
  flex-shrink: 0;
}

.last-week-sales-card .compare-checkbox img,
.monthly-sales-card .compare-checkbox img {
  width: 20px; /* Increased from 14px to 20px */
  height: 20px; /* Increased from 14px to 20px */
  transition: opacity 0.2s ease;
}

.last-week-sales-card .compare-dropdown-text,
.monthly-sales-card .compare-dropdown-text {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px; /* Match marketplace dropdown font size */
  line-height: 1.2;
  color: #606F95;
  flex: 1;
}

[data-theme="dark"] .last-week-sales-card .compare-dropdown-text,
[data-theme="dark"] .monthly-sales-card .compare-dropdown-text {
  color: var(--text-primary) !important; /* Match marketplace dropdown dark text color */
}

/* View Insights Button */
.view-insights-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 5px 10px;
  height: 32px;
  background: #FFFFFF;
  border: 1px solid #E9EBF2;
  border-radius: 4px;
  cursor: pointer;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #606F95;
  box-sizing: border-box;
  white-space: nowrap;
}

.view-insights-btn span {
  margin-top: 2px;
}

.view-insights-btn:hover {
  background: #F7F8FA;
  border-color: #DCE0E5;
}

[data-theme="dark"] .view-insights-btn {
  background: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .view-insights-btn:hover {
  background: var(--btn-hover);
  border-color: var(--border-color);
}

/* Monthly Sales Year Dropdown */
.monthly-sales-year-dropdown {
  display: flex;
  align-items: center;
  position: relative;
  width: 110px;
}

.monthly-sales-year-dropdown .dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 12px;
  height: 32px;
  background: #FFFFFF;
  border: 1.5px solid #E9EBF2;
  border-radius: 4px;
  cursor: pointer;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #606F95;
  box-sizing: border-box;
  width: 100%;
}

.monthly-sales-year-dropdown.focused .dropdown-header {
  border-color: #470CED !important;
}

.monthly-sales-year-dropdown .dropdown-header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  text-align: left;
}

.monthly-sales-year-dropdown .dropdown-arrow {
  width: 15px;
  height: 15px;
  flex-shrink: 0;
  margin: 0 !important;
}
.monthly-sales-year-dropdown .dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: #FFFFFF;
  border: 1.5px solid #E9EBF2;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: var(--z-dropdown); /* Standardized z-index for dropdown */
  will-change: transform;
  margin-top: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-height: none;
  overflow: visible;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

/* Hide scrollbar for WebKit browsers */
.monthly-sales-year-dropdown .dropdown-menu::-webkit-scrollbar {
  display: none;
}

.monthly-sales-year-dropdown .dropdown-menu.hidden {
  display: none;
}

/* Prevent double scrollbars - override general dropdown list styles */
.monthly-sales-year-dropdown .dropdown-list {
  max-height: none !important;
  overflow-y: visible !important;
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

/* Hide scrollbar for WebKit browsers */
.monthly-sales-year-dropdown .dropdown-list::-webkit-scrollbar {
  display: none !important;
}

/* Remove left padding from dropdown selected label */
.monthly-sales-year-dropdown .dropdown-selected-label {
  padding-left: 0 !important;
}

.monthly-sales-year-dropdown .dropdown-item {
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #606F95;
}

.monthly-sales-year-dropdown .dropdown-item:first-child:hover {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

.monthly-sales-year-dropdown .dropdown-item:last-child:hover {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.monthly-sales-year-dropdown .dropdown-item:hover {
  background: #F3F4F6;
}

.monthly-sales-year-dropdown .dropdown-item.selected {
  background: transparent;
  color: #18181B; /* Keep normal text color for selected item */
}

[data-theme="dark"] .monthly-sales-year-dropdown .dropdown-header {
  background: var(--bg-primary);
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary);
}

[data-theme="dark"] .monthly-sales-year-dropdown .dropdown-header:hover,
[data-theme="dark"] .monthly-sales-year-dropdown.focused .dropdown-header:hover {
  background: var(--btn-hover);
}

[data-theme="dark"] .monthly-sales-year-dropdown.focused .dropdown-header {
  border-color: #470CED !important;
}

[data-theme="dark"] .monthly-sales-year-dropdown .dropdown-menu {
  background: var(--bg-primary);
  border: 1.5px solid var(--border-color) !important;
  border-radius: 4px;
}

[data-theme="dark"] .monthly-sales-year-dropdown .dropdown-item {
  color: var(--text-primary);
}

[data-theme="dark"] .monthly-sales-year-dropdown .dropdown-item span {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .monthly-sales-year-dropdown .dropdown-item:hover,
[data-theme="dark"] .monthly-sales-year-dropdown .dropdown-item.selected {
  background: #292E38 !important;
  color: var(--text-accent) !important;
}

[data-theme="dark"] .monthly-sales-year-dropdown .dropdown-item:first-child:hover,
[data-theme="dark"] .monthly-sales-year-dropdown .dropdown-item:first-child.selected {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

[data-theme="dark"] .monthly-sales-year-dropdown .dropdown-item:last-child:hover,
[data-theme="dark"] .monthly-sales-year-dropdown .dropdown-item:last-child.selected {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

/* Chart Container */
.last-week-chart-container,
.monthly-sales-chart-container,
.yearly-sales-chart-container,
.today-vs-previous-years-chart-container {
  width: 100%;
  height: 340px; /* Increased from 320px to 340px to accommodate larger canvas and prevent font compression */
  position: relative;
  margin-top: 0px;
  font-family: 'Amazon Ember', Arial, sans-serif; /* Ensure consistent font rendering */
}

/* Ensure chart container loaders are always on top */
.last-week-chart-container > .snap-loader-overlay,
.monthly-sales-chart-container > .snap-loader-overlay,
.yearly-sales-chart-container > .snap-loader-overlay,
.today-vs-previous-years-chart-container > .snap-loader-overlay {
  z-index: var(--z-modal) !important;
}

/* Hide native scrollbars in Today vs Previous Years chart - only show custom scrollbar */
.today-vs-previous-years-chart-container * {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

.today-vs-previous-years-chart-container *::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
}

.today-vs-previous-years-chart-container *::-webkit-scrollbar:horizontal {
  display: none !important;
  height: 0 !important;
}

.today-vs-previous-years-chart-container *::-webkit-scrollbar:vertical {
  display: none !important;
  width: 0 !important;
}

.today-vs-previous-years-chart-container *::-webkit-scrollbar-track {
  display: none !important;
  background: transparent !important;
}

.today-vs-previous-years-chart-container *::-webkit-scrollbar-thumb {
  display: none !important;
  background: transparent !important;
}

.today-vs-previous-years-chart-container *::-webkit-scrollbar-corner {
  display: none !important;
  background: transparent !important;
}

/* Marketplace Focus Dropdown Container for Today vs Previous Years Card */
.today-vs-previous-years-card .marketplace-focus-dropdown-container {
  display: flex;
  align-items: center;
  gap: 0; /* Remove gap to fix alignment */
  margin-left: 0; /* Remove left margin */
  padding-left: 0; /* Remove left padding */
  min-width: unset; /* Remove min-width constraint to allow natural sizing */
}

/* Marketplace Focus Dropdown Width Matching - Specific to Marketplace Focus dropdown only */
#marketplaceDropdown.database-marketplace-dropdown .dropdown-menu {
  width: 100% !important; /* Ensure dropdown menu matches header width exactly */
  min-width: 100% !important; /* Prevent any minimum width constraints */
  max-width: 100% !important; /* Prevent any maximum width constraints */
  box-sizing: border-box !important; /* Include padding and border in width calculation */
}

#marketplaceDropdown.database-marketplace-dropdown .dropdown-list {
  width: 100% !important; /* Ensure dropdown list matches header width exactly */
  min-width: 100% !important; /* Prevent any minimum width constraints */
  max-width: 100% !important; /* Prevent any maximum width constraints */
  box-sizing: border-box !important; /* Include padding and border in width calculation */
}

/* Ensure the dropdown header width is properly calculated for the Marketplace Focus dropdown */
#marketplaceDropdown.database-marketplace-dropdown .dropdown-header {
  width: auto !important; /* Allow natural width calculation */
  max-width: 220px !important; /* Maintain maximum width */
  box-sizing: border-box !important; /* Include padding and border in width calculation */
}

/* Ensure width matching works in dark theme for Marketplace Focus dropdown */
[data-theme="dark"] #marketplaceDropdown.database-marketplace-dropdown .dropdown-menu {
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

[data-theme="dark"] #marketplaceDropdown.database-marketplace-dropdown .dropdown-list {
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Ensure responsive behavior maintains width matching for Marketplace Focus dropdown */
@media (max-width: 1200px) {
  #marketplaceDropdown.database-marketplace-dropdown .dropdown-menu,
  #marketplaceDropdown.database-marketplace-dropdown .dropdown-list {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
  }
}

@media (max-width: 800px) {
  #marketplaceDropdown.database-marketplace-dropdown .dropdown-menu,
  #marketplaceDropdown.database-marketplace-dropdown .dropdown-list {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
  }
}

.today-vs-previous-years-card .controls-section {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  margin-left: auto;
  margin-right: 0;
  padding-right: 0;
  padding-left: 0;
  gap: 0; /* Remove any gap */
}

/* Ensure the dropdown itself has no margins or padding */
.today-vs-previous-years-card .dropdown,
.today-vs-previous-years-card .database-marketplace-dropdown,
.today-vs-previous-years-card .snap-dropdown {
  margin: 0;
  padding: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .last-week-sales-card .Sales-title-date-div,
  .monthly-sales-card .Sales-title-date-div,
  .today-vs-previous-years-card .Sales-title-date-div,
  .yearly-sales-card .Sales-title-date-div {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .last-week-sales-card .controls-section,
  .monthly-sales-card .controls-section,
  .today-vs-previous-years-card .controls-section,
  .yearly-sales-card .controls-section {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .last-week-sales-card,
  .monthly-sales-card,
  .yearly-sales-card,
  .today-vs-previous-years-card {
    padding: 16px;
  }

  .last-week-chart-container,
  .monthly-sales-chart-container,
  .yearly-sales-chart-container,
  .today-vs-previous-years-chart-container {
    height: 270px; /* Increased from 250px to 270px to maintain proportions and prevent font compression on mobile */
  }

  /* Stack controls vertically on mobile for better usability */
  .last-week-sales-card .controls-section,
  .monthly-sales-card .controls-section,
  .today-vs-previous-years-card .controls-section,
  .yearly-sales-card .controls-section {
    flex-wrap: wrap;
    gap: 8px;
  }
}

/* Responsive adjustments for no-sales state */
@media (max-width: 600px) {
  .no-sales-state {
    flex-direction: column;
    gap: 12px;
    min-height: 160px; /* Smaller minimum height on mobile */
    margin-top: 16px; /* Reduced margin on mobile */
  }

  .no-sales-img {
    width: 80px;
    height: 88px;
  }

  .no-sales-text {
    align-items: center;
    text-align: center;
  }

  /* Responsive adjustments for no-reviews state */
  .no-reviews-state {
    flex-direction: column;
    gap: 12px;
    min-height: 160px; /* Smaller minimum height on mobile */
    margin-top: 16px; /* Reduced margin on mobile */
  }
  .no-reviews-img {
    width: 80px;
    height: 88px;
  }
  .no-reviews-text {
    align-items: center;
    text-align: center;
  }

  /* Scoped mobile override: keep empty state height equal to icon in reviews card */
  .insights-and-feedback-section .customer-reviews-card-div .no-reviews-state {
    flex-direction: row;
    gap: 16px;
    min-height: 0;
    height: auto;
    margin: auto 0; /* center vertically in available space on mobile too */
  }
  .insights-and-feedback-section .customer-reviews-card-div .no-reviews-img {
    width: 80px;
    height: 88px;
  }
  .insights-and-feedback-section .customer-reviews-card-div .no-reviews-text {
    align-items: flex-start;
    text-align: left;
  }

  /* Responsive adjustments for no-payout state */
  .no-payout-state {
    flex-direction: column;
    gap: 12px;
    min-height: 160px; /* Smaller minimum height on mobile */
    margin-top: 16px; /* Reduced margin on mobile */
  }
  .no-payout-img {
    width: 80px;
    height: 88px;
  }
  .no-payout-text {
    align-items: center;
    text-align: center;
  }
}

/* New Tab Count Styling - Only the number in green */
.new-tab .tab-label .count-green {
  color: #04AE2C !important;
  font-weight: 700 !important;
}

/* New Tab Dimmed State - 50% opacity when no new sellers */
/* Apply opacity to the visible tab content, not the container */
.new-tab.no-new-sellers {
  cursor: not-allowed;
}

.new-tab.no-new-sellers .tab-main,
.new-tab.no-new-sellers .tab-label,
.new-tab.no-new-sellers .tab-icon {
  opacity: 0.5;
}

.new-tab.no-new-sellers .tab-label {
  cursor: not-allowed;
}

/* Dark theme support for new tab count */
[data-theme="dark"] .new-tab .tab-label .count-green {
  color: #04AE2C !important;
}

/* --- BADGE GRADIENTS & STROKES (Figma accurate) --- */

/* Lock Badge - Icon only, red gradient style */
.lock-badge {
  background: linear-gradient(90deg, rgba(255,57,31,0) 0%, rgba(255,57,31,0.10) 100%) !important;
  border: 1.3px solid rgba(255,57,31,0.2) !important;
  color: #FF391F !important;
  border-radius: 6px !important;
  font-weight: 500;
  padding: 0 !important; /* Remove padding for exact sizing */
  width: 20px !important; /* Exact 20px width */
  height: 20px !important; /* Exact 20px height */
  min-width: 20px !important;
  min-height: 20px !important;
  max-width: 20px !important;
  max-height: 20px !important;
  justify-content: center !important;
  display: flex !important;
  align-items: center !important;
}
.lock-badge img {
  width: 12px !important;
  height: 12px !important;
  min-width: 12px !important;
  min-height: 12px !important;
  max-width: 12px !important;
  max-height: 12px !important;
  margin: 0 !important;
  color: #FF391F !important;
  fill: #FF391F !important;
  filter: none !important;
  vertical-align: middle !important;
}

/* Dark theme support for lock badge */
[data-theme="dark"] .lock-badge {
  background: linear-gradient(90deg, rgba(255,57,31,0) 0%, rgba(255,57,31,0.18) 100%) !important;
  border: 1.3px solid rgba(255,57,31,0.3) !important;
  color: #FF391F !important;
}
[data-theme="dark"] .lock-badge img {
  color: #FF391F !important;
  fill: #FF391F !important;
  filter: none !important;
}

/* Lost Royalties Badge */

/* Privacy Mode Styles */
.privacy-mode { display: flex; align-items: center; gap: 10px; }

.privacy-mode-label {
  display: flex;
  align-items: center;
  height: 40px;
  white-space: nowrap;
}

.privacy-mode-label span {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #606F95;
}

.privacy-mode-toggle {
  width: auto !important;
  height: 40px !important;
  min-height: 40px !important;
  max-height: 40px !important;
  padding: 3px !important;
  background: rgba(232, 235, 244, 0.5) !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  gap: 3px !important;
  box-sizing: border-box !important;
}

[data-theme="dark"] .privacy-mode-toggle {
  background: #292E38 !important;
}

.privacy-mode-toggle .off-tab,
.privacy-mode-toggle .on-tab {
  flex: 1;
  height: 100%;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: transparent;
}

.privacy-mode-toggle .off-tab.active,
.privacy-mode-toggle .on-tab.active {
  background: #fff;
  box-shadow: 0 2px 8px rgba(96, 111, 149, 0.04);
}

/* Hover state for inactive privacy toggle tabs only */
.privacy-mode-toggle .off-tab:not(.active):hover,
.privacy-mode-toggle .on-tab:not(.active):hover {
  background: rgba(96, 111, 149, 0.04);
  transition: background 0.2s ease;
}

[data-theme="dark"] .privacy-mode-toggle .off-tab.active,
[data-theme="dark"] .privacy-mode-toggle .on-tab.active {
  background: var(--bg-primary);
  box-shadow: none;
}

/* Dark theme hover state for inactive privacy toggle tabs only */
[data-theme="dark"] .privacy-mode-toggle .off-tab:not(.active):hover,
[data-theme="dark"] .privacy-mode-toggle .on-tab:not(.active):hover {
  background: rgba(255, 255, 255, 0.05);
}

.privacy-mode-toggle .off-div,
.privacy-mode-toggle .on-div {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 16px;
}

.privacy-mode-toggle .off-div img,
.privacy-mode-toggle .on-div img {
  width: 16px;
  height: 16px;
}

.privacy-mode-toggle .off-div span,
.privacy-mode-toggle .on-div span {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 11px;
  font-weight: 500;
  color: var(--text-primary);
  margin-top: 2px;
}

.privacy-mode-toggle .off-tab.active .off-div span {
  color: #470CED;
  font-weight: 700;
}

.privacy-mode-toggle .on-tab.active .on-div span {
  color: #470CED;
  font-weight: 700;
}

[data-theme="dark"] .privacy-mode-label span {
  color: #FFFFFF !important;
}

[data-theme="dark"] .privacy-mode-toggle .off-div span,
[data-theme="dark"] .privacy-mode-toggle .on-div span {
  color: #B4B9C5;
}

/* Dark theme active state text and icon colors for privacy toggle */
[data-theme="dark"] .privacy-mode-toggle .off-tab.active .off-div span,
[data-theme="dark"] .privacy-mode-toggle .on-tab.active .on-div span {
  color: #FFFFFF;
  font-weight: 700;
}

[data-theme="dark"] .privacy-mode-toggle .off-tab.active .off-div img,
[data-theme="dark"] .privacy-mode-toggle .on-tab.active .on-div img {
  filter: brightness(0) invert(1);
}

/* New Privacy Toggle (single-switch) */
.privacy-toggle-btn {
  width: 40px;
  height: 40px;
  background: #E8EBF4;
  border-radius: 100px;
  padding: 1px;
  border: 1px solid #E9EBF2;
  box-shadow: 0 2px 8px rgba(96, 111, 149, 0.04);
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

[data-theme="dark"] .privacy-toggle-btn {
  background: #292E38;
  border-color: transparent;
}

.privacy-toggle-btn .toggle {
  width: 34px;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.privacy-toggle-btn .toggle:last-child { left: 50%; top: 50%; transform: translate(-50%, -50%); }

.privacy-toggle-btn .toggle { opacity: 0; visibility: hidden; }
.privacy-toggle-btn .toggle-off.active { background: #FFFFFF; z-index: var(--z-surface); opacity: 1; visibility: visible; }
.privacy-toggle-btn .toggle-on.active { background: #470CED; z-index: var(--z-surface); opacity: 1; visibility: visible; }

[data-theme="dark"] .privacy-toggle-btn .toggle-off.active { background: #1B1D21; }
[data-theme="dark"] .privacy-toggle-btn .toggle-on.active { background: #470CED; }

/* Dashboard Refresh Button (separate control) */
.dashboard-refresh-btn {
  width: 40px;
  height: 40px;
  background: #E8EBF4;
  border-radius: 100px;
  padding: 1px;
  border: 1px solid #E9EBF2;
  box-shadow: 0 2px 8px rgba(96, 111, 149, 0.04);
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

[data-theme="dark"] .dashboard-refresh-btn {
  background: #292E38;
  border-color: transparent;
}

.dashboard-refresh-btn .refresh-inner {
  width: 34px;
  height: 34px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 0;
}

[data-theme="dark"] .dashboard-refresh-btn .refresh-inner { background: #1B1D21; }

.dashboard-refresh-btn .refresh-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-refresh-btn .refresh-icon img {
  width: 16px;
  height: 16px;
  display: block;
}

[data-theme="dark"] .dashboard-refresh-btn .refresh-icon img {
  filter: brightness(0) invert(1);
}

/* Dashboard Refresh Button - Refreshing State */
.dashboard-refresh-btn.refreshing {
  cursor: not-allowed;
  opacity: 0.7;
}

.dashboard-refresh-btn.refreshing .refresh-icon img {
  animation: refresh-spin 1s linear infinite;
}

/* Refresh spin animation */
@keyframes refresh-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Disabled state for refresh button */
.dashboard-refresh-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.dashboard-refresh-btn:disabled .refresh-icon img {
  animation: refresh-spin 1s linear infinite;
}

.privacy-icon {
  width: 16px;
  height: 16px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.privacy-icon img {
  width: 16px;
  height: 16px;
  color: #384459;
}

[data-theme="dark"] .privacy-toggle-btn .toggle.active .privacy-icon img {
  color: #FFFFFF;
  filter: brightness(0) invert(1);
}

/* Privacy Mode UI States */
.listing-left-div.state-private {
  background: #470CED;
  position: relative;
  overflow: hidden;
}

.listing-left-div.state-private::before {
  content: '';
  position: absolute;
  top: 12px;
  left: 12px;
  width: 24px;
  height: 24px;
  background-image: url('./assets/privacy-mode-ic.svg');
  background-size: contain;
  background-repeat: no-repeat;
  z-index: var(--z-surface);
}

.listing-left-div.state-private::after {
  content: 'Privacy Mode';
  position: absolute;
  bottom: 12px;
  left: 12px;
  right: 12px;
  color: #FFFFFF;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.2;
  text-align: left;
  z-index: var(--z-surface);
}

/* Database right container styles */
.database-right {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 0 0 auto; /* Don't grow or shrink, maintain natural size */
  /* Let the content determine the natural width, no artificial min-width */
  margin-left: auto;
}

/* ============================================================================
   GLOBAL LOADER SYSTEM
   ============================================================================ */

/* Loader Animation - Core spin animation used across all loaders */
@keyframes snap-loader-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Fade-in animation for smooth loader appearance */
@keyframes snap-loader-fade-in {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

/* ============================================================================
   LOADER VARIANTS
   ============================================================================ */

/* 1. OVERLAY LOADER - Full screen/container overlay with spinner and text */
.snap-loader-overlay {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  width: 100%;
  height: 100%;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box;
  border-radius: inherit;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: var(--z-modal);
  background-color: #FFFFFF; /* remove opacity */
  font-family: 'Amazon Ember', Arial, sans-serif;
  animation: snap-loader-fade-in 0.2s ease-out;
}

/* Viewport overlay variant - fixed to viewport, centers content regardless of scroll */
.snap-loader-overlay.snap-loader-viewport {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  width: 100vw;
  height: 100vh;
  border-radius: 0;
  pointer-events: all; /* block interactions behind overlay */
  z-index: var(--z-overlay); /* ensure above all app content */
}

/* Prevent hover states and interactions for elements behind the loader */
/* When a container has a snap-loader-overlay, disable interactions on all other children */
*:has(> .snap-loader-overlay) > *:not(.snap-loader-overlay) {
  pointer-events: none !important;
}

*:has(> .snap-loader-overlay) > *:not(.snap-loader-overlay):hover,
*:has(> .snap-loader-overlay) > *:not(.snap-loader-overlay) :hover {
  background-color: inherit !important;
  color: inherit !important;
  border-color: inherit !important;
  box-shadow: inherit !important;
  transform: none !important;
  opacity: inherit !important;
  filter: none !important;
}

/* Fallback for browsers that don't support :has() */
.snap-loader-active {
  pointer-events: none !important;
}
.snap-loader-active > *:not(.snap-loader-overlay) {
  pointer-events: none !important;
}

.snap-loader-active > *:not(.snap-loader-overlay):hover,
.snap-loader-active > *:not(.snap-loader-overlay) :hover {
  background-color: inherit !important;
  color: inherit !important;
  border-color: inherit !important;
  box-shadow: inherit !important;
  transform: none !important;
  opacity: inherit !important;
  filter: none !important;
}
/* Payout cards: hide section dividers when card-level empty state is visible (runtime style uses inline display flex) */
.next-payout-card-div:has(.no-payout-state[style*="display: flex"]) .sales-section-divider,
.previous-payout-card-div:has(.no-payout-state[style*="display: flex"]) .sales-section-divider {
  display: none !important;
}
[data-theme="dark"] .snap-loader-overlay {
  background-color: #1A1D23; /* remove opacity */
}

/* Overlay Spinner */
.snap-loader-overlay .snap-loader-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(96, 111, 149, 0.1);
  border-radius: 50%;
  border-left-color: #470CED;
  animation: snap-loader-spin 1s linear infinite;
  margin-bottom: 16px;
  box-sizing: border-box;
}

[data-theme="dark"] .snap-loader-overlay .snap-loader-spinner {
  border-color: rgba(180, 185, 197, 0.1);
  border-left-color: #470CED;
}

/* Overlay Text */
.snap-loader-overlay .snap-loader-text {
  font-weight: 500;
  font-size: 16px;
  color: #606F95;
  text-align: center;
  margin: 0;
  line-height: 1.4;
}

[data-theme="dark"] .snap-loader-overlay .snap-loader-text {
  color: #B4B9C5;
}

/* 2. INLINE LOADER - Small loader for inputs and inline elements */
.snap-loader-inline {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 1.5px solid rgba(96, 111, 149, 0.1);
  border-radius: 50%;
  border-left-color: #470CED;
  animation: snap-loader-spin 1s linear infinite;
  pointer-events: none;
  display: none;
  box-sizing: border-box;
}

[data-theme="dark"] .snap-loader-inline {
  border-color: rgba(180, 185, 197, 0.1);
  border-left-color: #470CED;
}

/* Input-specific positioning */
.snap-loader-inline.input-loader {
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
}

/* Button-specific positioning */
.snap-loader-inline.button-loader {
  position: relative;
  display: inline-block;
  margin-right: 8px;
  top: 0;
  transform: none;
}

/* 3. SMALL LOADER - Compact loader for tight spaces */
.snap-loader-small {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(96, 111, 149, 0.1);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: snap-loader-spin 1s linear infinite;
  display: inline-block;
  box-sizing: border-box;
}

[data-theme="dark"] .snap-loader-small {
  border-color: rgba(180, 185, 197, 0.1);
  border-top-color: transparent;
}

/* Small loader with theme-aware accent color */
.snap-loader-small.accent {
  border-left-color: #470CED;
}

[data-theme="dark"] .snap-loader-small.accent {
  border-left-color: #470CED;
}

/* 4. MEDIUM LOADER - Standard size for cards and sections */
.snap-loader-medium {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(96, 111, 149, 0.1);
  border-radius: 50%;
  border-left-color: #470CED;
  animation: snap-loader-spin 1s linear infinite;
  display: inline-block;
  box-sizing: border-box;
}

[data-theme="dark"] .snap-loader-medium {
  border-color: rgba(180, 185, 197, 0.1);
  border-left-color: #470CED;
}

/* 5. LARGE LOADER - For major loading operations */
.snap-loader-large {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(96, 111, 149, 0.1);
  border-radius: 50%;
  border-left-color: #470CED;
  animation: snap-loader-spin 1s linear infinite;
  display: inline-block;
  box-sizing: border-box;
}

[data-theme="dark"] .snap-loader-large {
  border-color: rgba(180, 185, 197, 0.1);
  border-left-color: #470CED;
}

/* ============================================================================
   LOADER STATES & MODIFIERS
   ============================================================================ */

/* Success state - green accent */
.snap-loader-success .snap-loader-spinner,
.snap-loader-success.snap-loader-inline,
.snap-loader-success.snap-loader-small,
.snap-loader-success.snap-loader-medium,
.snap-loader-success.snap-loader-large {
  border-left-color: #04AE2C;
}

/* Error state - red accent */
.snap-loader-error .snap-loader-spinner,
.snap-loader-error.snap-loader-inline,
.snap-loader-error.snap-loader-small,
.snap-loader-error.snap-loader-medium,
.snap-loader-error.snap-loader-large {
  border-left-color: #FF391F;
}

/* Warning state - orange accent */
.snap-loader-warning .snap-loader-spinner,
.snap-loader-warning.snap-loader-inline,
.snap-loader-warning.snap-loader-small,
.snap-loader-warning.snap-loader-medium,
.snap-loader-warning.snap-loader-large {
  border-left-color: #F77F16;
}

/* Slow animation for long operations */
.snap-loader-slow .snap-loader-spinner,
.snap-loader-slow.snap-loader-inline,
.snap-loader-slow.snap-loader-small,
.snap-loader-slow.snap-loader-medium,
.snap-loader-slow.snap-loader-large {
  animation-duration: 2s;
}

/* Fast animation for quick operations */
.snap-loader-fast .snap-loader-spinner,
.snap-loader-fast.snap-loader-inline,
.snap-loader-fast.snap-loader-small,
.snap-loader-fast.snap-loader-medium,
.snap-loader-fast.snap-loader-large {
  animation-duration: 0.5s;
}

/* ============================================================================
   LOADER UTILITIES
   ============================================================================ */

/* Hide element while showing loader */
.snap-loader-hidden {
  opacity: 0;
  pointer-events: none;
}

/* Container with relative positioning for absolute loaders */
.snap-loader-container {
  position: relative;
}

/* Disable interactions while loading */
.snap-loader-disabled {
  pointer-events: none;
  opacity: 0.6;
  cursor: not-allowed;
}

/* Button loading state */
.snap-loader-button-loading {
  position: relative;
  pointer-events: none;
  opacity: 0.8;
}

.snap-loader-button-loading::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-left-color: #FFFFFF;
  animation: snap-loader-spin 1s linear infinite;
  z-index: var(--z-base);
}

/* Dark theme button loading */
[data-theme="dark"] .snap-loader-button-loading::before {
  border-color: rgba(180, 185, 197, 0.3);
  border-left-color: #FFFFFF;
}

/* ============================================================================
   COMPONENT INTEGRATION
   ============================================================================ */

/* Sales card loading state */
.todays-sales-card-div.loading,
.yesterdays-sales-card-div.loading,
.current-month-card-div.loading,
.last-month-card-div.loading,
.current-year-card-div.loading,
.last-year-card-div.loading {
  position: relative;
}

.todays-sales-card-div.loading::after,
.yesterdays-sales-card-div.loading::after,
.current-month-card-div.loading::after,
.last-month-card-div.loading::after,
.current-year-card-div.loading::after,
.last-year-card-div.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-dropdown);
}

[data-theme="dark"] .todays-sales-card-div.loading::after,
[data-theme="dark"] .yesterdays-sales-card-div.loading::after,
[data-theme="dark"] .current-month-card-div.loading::after,
[data-theme="dark"] .last-month-card-div.loading::after,
[data-theme="dark"] .current-year-card-div.loading::after,
[data-theme="dark"] .last-year-card-div.loading::after {
  background: var(--bg-primary);
}

/* Sidebar button loading state */
.sidebar-btn.loading .sidebar-icon {
  opacity: 0.5;
}

.sidebar-btn.loading::after {
  content: '';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  border: 1.5px solid rgba(96, 111, 149, 0.2);
  border-radius: 50%;
  border-left-color: #470CED;
  animation: snap-loader-spin 1s linear infinite;
}

[data-theme="dark"] .sidebar-btn.loading::after {
  border-color: rgba(180, 185, 197, 0.2);
  border-left-color: #470CED;
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 600px) {
  /* Smaller overlay spinner on mobile */
  .snap-loader-overlay .snap-loader-spinner {
    width: 32px;
    height: 32px;
    border-width: 3px;
    margin-bottom: 12px;
  }

  /* Smaller overlay text on mobile */
  .snap-loader-overlay .snap-loader-text {
    font-size: 14px;
  }

  /* Adjust large loader for mobile */
  .snap-loader-large {
    width: 36px;
    height: 36px;
    border-width: 3px;
  }
}

/* ============================================================================
   ACCESSIBILITY
   ============================================================================ */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .snap-loader-spinner,
  .snap-loader-inline,
  .snap-loader-small,
  .snap-loader-medium,
  .snap-loader-large,
  .snap-loader-button-loading::before,
  .sidebar-btn.loading::after {
    animation-duration: 2s;
  }
}

/* Screen reader support */
.snap-loader-overlay[aria-label]::after,
.snap-loader-inline[aria-label]::after,
.snap-loader-small[aria-label]::after,
.snap-loader-medium[aria-label]::after,
.snap-loader-large[aria-label]::after {
  content: attr(aria-label);
  position: absolute;
  left: -9999px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Focus management during loading */
.snap-loader-overlay:focus,
.snap-loader-container:focus-within .snap-loader-overlay {
  outline: 2px solid #470CED;
  outline-offset: 2px;
}

/* Compact loader overlay for small containers (e.g., ad-spend) */
.snap-loader-overlay.snap-loader-compact {
  padding: 12px 0;
}
.snap-loader-overlay.snap-loader-compact .snap-loader-spinner {
  /* Do not override width/height, use default (medium) */
  margin-bottom: 8px;
}
.snap-loader-overlay.snap-loader-compact .snap-loader-text {
  font-size: 13px;
  font-weight: 400;
  margin: 0;
  padding: 0;
}

/* Loader overlay should match ad-spend container shape and size */
.ad-spend > .snap-loader-overlay {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  width: 100%;
  height: 100%;
  border-radius: 14px;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: var(--z-modal);
  background-color: #FFFFFF; /* remove opacity */
  /* Inherit font and transitions */
  font-family: 'Amazon Ember', Arial, sans-serif;
  animation: snap-loader-fade-in 0.2s ease-out;
}
[data-theme="dark"] .ad-spend > .snap-loader-overlay {
  background-color: #1A1D23; /* remove opacity */
}

.snap-loader-overlay,
.ad-spend > .snap-loader-overlay {
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box;
}

/* Listings Status Overview: Horizontal loader layout (compact height) */
.listings-status-overview > .snap-loader-overlay {
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.listings-status-overview > .snap-loader-overlay .snap-loader-spinner {
  margin-bottom: 0;
  margin-right: 10px;
  width: 24px;
  height: 24px;
  border-width: 3px;
}
.listings-status-overview > .snap-loader-overlay .snap-loader-text {
  text-align: left;
  font-size: 14px;
  line-height: 24px; /* match spinner size for perfect vertical alignment */
  display: inline-flex;
  align-items: center;
}

/* Hide Quarterly grid while Monthly Sales chart shows an overlay loader */
.monthly-sales-card:has(#monthly-sales-chart-container > .snap-loader-overlay) #quarter-sales-div {
  visibility: hidden;
}

.drag-drop-area,
.tab,
.tab-container {
  /* transition: var(--theme-transition); */
}

.drag-drop-area:hover,
.tab:hover,
.tab-container:hover {
  transition: background-color 0.3s ease;
}

.loaded-files-container,
.loaded-files-container.visible,
.processing-files-container {
  /* Removed transition: var(--theme-transition); */
}

/* If you want transitions on hover/feedback, add them only to those states, e.g.: */
.loaded-files-container:hover,
.processing-files-container:hover {
  transition: background 0.2s ease, border-color 0.2s ease;
}

/* Payout: avoid double spacing around divider in single marketplace focus */
.payout-cards-row .marketplaces-div.single-marketplace-active { margin-top: 0 !important; }

/* Hide listing-ad-row when it has ad-no-sales class or contains zero values */
.listing-ad-row.ad-no-sales {
  display: none !important;
}

/* Hide listing-ad-row when ad spend shows zero values or no sales */
.listing-ad-row.has-zero-ad-spend {
  display: none !important;
}

/* Dark mode border removal for containers and cards */
[data-theme="dark"] .sidebar-collapse-btn {
  border: none;
}

[data-theme="dark"] .tip-card {
  border: none;
}

[data-theme="dark"] .account-status {
  border: none;
}

[data-theme="dark"] .listings-status {
  border: none;
}

[data-theme="dark"] .listings-status-overview {
  border: none;
}

[data-theme="dark"] .ad-spend {
  border: none;
}

[data-theme="dark"] .todays-sales-card-div,
[data-theme="dark"] .yesterdays-sales-card-div,
[data-theme="dark"] .current-month-card-div,
[data-theme="dark"] .last-month-card-div,
[data-theme="dark"] .current-year-card-div,
[data-theme="dark"] .last-year-card-div {
  border: none;
}

[data-theme="dark"] .last-week-sales-card {
  border: none;
}

[data-theme="dark"] .today-vs-previous-years-card {
  border: none;
}

[data-theme="dark"] .snap-dropdown .dropdown-header {
  background: var(--bg-primary);
  border: 1.5px solid var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .snap-dropdown .dropdown-menu {
  border: none;
  scrollbar-color: #2F3341 transparent !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-item:hover {
  background: #292E38 !important;
  color: var(--text-accent) !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-item.selected {
  background: #292E38 !important;
  color: #FFFFFF !important;
}

/* Dark mode scrollbar for dropdown list */
[data-theme="dark"] .snap-dropdown .dropdown-list::-webkit-scrollbar-thumb {
    background: #2F3341 !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-list::-webkit-scrollbar-thumb:hover {
    background: #B4B9C5 !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-list {
    scrollbar-color: #2F3341 transparent !important;
}

[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu {
  border: none;
}

[data-theme="dark"] .last-week-sales-card .show-hide-options-dropdown {
  border: none;
}

[data-theme="dark"] .last-week-sales-card .compare-dropdown {
  border: none;
}



[data-theme="dark"] .monthly-sales-year-dropdown .dropdown-menu {
  border: none;
}

[data-theme="dark"] .sales-filter-tab.active {
  border: none;
}

[data-theme="dark"] .privacy-mode-toggle .off-tab.active {
  border: none;
}

[data-theme="dark"] .privacy-mode-toggle .on-tab.active {
  border: none;
}

/* Remove borders from chart containers in dark mode */
[data-theme="dark"] .last-week-chart-container,
[data-theme="dark"] .today-vs-previous-years-chart-container {
  border: none;
}

/* Remove borders from any other card-like elements */
[data-theme="dark"] [class*="-card"],
[data-theme="dark"] [class*="-container"] {
  border: none;
}

/* Ensure backgrounds are preserved while removing borders */
[data-theme="dark"] .account-status,
[data-theme="dark"] .listings-status,
[data-theme="dark"] .listings-status-overview,
[data-theme="dark"] .ad-spend,
[data-theme="dark"] .todays-sales-card-div,
[data-theme="dark"] .yesterdays-sales-card-div,
[data-theme="dark"] .current-month-card-div,
[data-theme="dark"] .last-month-card-div,
[data-theme="dark"] .current-year-card-div,
[data-theme="dark"] .last-year-card-div,
[data-theme="dark"] .last-week-sales-card,
[data-theme="dark"] .today-vs-previous-years-card,
[data-theme="dark"] .monthly-sales-card,
[data-theme="dark"] .yearly-sales-card,
[data-theme="dark"] .top-day-card-div,
[data-theme="dark"] .top-week-card-div,
[data-theme="dark"] .top-month-card-div,
[data-theme="dark"] .top-year-card-div {
  background: var(--bg-primary);
}

/* Remove borders from dashboard cards in both light and dark modes */
/* Account & Status Cards */
.account-status,
.listings-status-overview,
.ad-spend {
  border: none;
}

/* Sales Cards */
.todays-sales-card-div,
.yesterdays-sales-card-div,
.current-month-card-div,
.last-month-card-div,
.current-year-card-div,
.last-year-card-div {
  border: none;
}

/* Chart Cards */
.last-week-sales-card,
.today-vs-previous-years-card,
.monthly-sales-card,
.yearly-sales-card {
  border: none;
}

/* Top Performance Cards */
.top-day-card-div,
.top-week-card-div,
.top-month-card-div,
.top-year-card-div {
  border: none;
}

/* Dark mode border removal - ensure no borders in dark mode either */
[data-theme="dark"] .account-status,
[data-theme="dark"] .listings-status-overview,
[data-theme="dark"] .ad-spend,
[data-theme="dark"] .todays-sales-card-div,
[data-theme="dark"] .yesterdays-sales-card-div,
[data-theme="dark"] .current-month-card-div,
[data-theme="dark"] .last-month-card-div,
[data-theme="dark"] .current-year-card-div,
[data-theme="dark"] .last-year-card-div,
[data-theme="dark"] .last-week-sales-card,
[data-theme="dark"] .today-vs-previous-years-card,
[data-theme="dark"] .monthly-sales-card,
[data-theme="dark"] .yearly-sales-card,
[data-theme="dark"] .top-day-card-div,
[data-theme="dark"] .top-week-card-div,
[data-theme="dark"] .top-month-card-div,
[data-theme="dark"] .top-year-card-div {
  border: none !important;
}

/* =====================================================================
   DROPDOWN STYLES REMOVED - All .snap-dropdown styling is now controlled
   by the respective component stylesheets (snap-grid.css, etc.)
   This ensures components maintain full control over their appearance.
   ===================================================================== */
