# Mock Data Update - Status and Product Categories

## Task Overview
Update mock data status and product categories to match the provided screenshots showing filter options.

## Requirements
- Update status list to match screenshot: Draft, Translating, Under Review, Declined, Rejected, Processing, Timed out, Auto-uploaded, Live, Removed, Locked
- Update product categories to match screenshot: Standard t-shirt, Premium t-shirt, V-neck t-shirt, Tank top, Long sleeve t-shirt, Ra<PERSON><PERSON>, Sweatshirt, Pullover hoodie, Zip hoodie, PopSockets, iPhone cases, Samsung Galaxy cases, Tote bag, Throw pillows, Tumbler
- Update dashboard mock data to include new status values

## Tasks

### [x] Task 1: Update Status Data
- ✅ Updated statuses array in dummy-grid-data.js to match screenshot
- ✅ Changed from: ['Active', 'Inactive', 'Pending', 'Draft', 'Archived', 'Processing']
- ✅ Changed to: ['Draft', 'Translating', 'Under Review', 'Declined', 'Rejected', 'Processing', 'Timed out', 'Auto-uploaded', 'Live', 'Removed', 'Locked']

### [x] Task 2: Update Product Categories
- ✅ Updated categories array in dummy-grid-data.js to match screenshot
- ✅ Changed from generic categories to specific product types
- ✅ Added all 15 product categories from screenshot

### [x] Task 3: Update Dashboard Status Data
- ✅ Updated listingsStatus in mock-dashboard-data.js
- ✅ Added new status fields: declined, underReview, removed
- ✅ Updated all marketplace entries with realistic values

### [x] Task 4: Code Quality Check
- ✅ Verified no linting errors in updated files
- ✅ Maintained existing code structure and patterns
- ✅ Added descriptive comments for new status fields

# Search Input Regression in Sales Cards

## Task Overview
Fix the styling regression affecting the sales cards' search input wrapper after recent asset path adjustments.

## Tasks

- [ ] Task 1: Audit CSS overrides impacting `.search-input-wrapper` across dashboard and grid contexts
- [ ] Task 2: Scope grid-specific styles to restore sales cards alignment and focus visuals
- [ ] Task 3: Verify styling in both contexts and document any follow-up work
