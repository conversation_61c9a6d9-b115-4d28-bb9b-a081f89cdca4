// Products Component rendering SnapGrid inside the Products page

const SNAP_GRID_CSS_PATH = 'components/data-grid/snap-grid.css';
const SNAP_GRID_SCRIPT_PATH = 'components/data-grid/snap-grid.js';
const CHUNKED_LOADER_SCRIPT_PATH = 'components/data-grid/chunked-data-loader.js';
const SNAP_GRID_ASSET_PREFIX = 'components/data-grid/assets/';
const TOTAL_PRODUCT_ROWS = 1_000_000;

function escapeHTML(value) {
    return String(value)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
}

const PRIVACY_PLACEHOLDER = 'Privacy Mode is enabled.';

function isPrivacyEnabledSync() {
    if (typeof window.isPrivacyModeEnabled === 'function') {
        return window.isPrivacyModeEnabled();
    }
    return localStorage.getItem('privacyMode') === 'enabled';
}

const PRODUCT_GRID_COLUMNS = [
    {
        field: 'marketplace',
        headerName: 'Marketplace',
        width: 100,
        sortable: true,
        filterable: true,
        cellRenderer: (value) => {
            if (!value) return '';
            const rawValue = String(value);
            const safeValue = escapeHTML(rawValue);
            const assetPath = `${SNAP_GRID_ASSET_PREFIX}${rawValue}.svg`;
            // Use inline styles matching snap-grid-test-updated.html for consistent rendering
            return `<img src="${assetPath}" alt="${safeValue}" style="width:16px;height:16px;margin-right:6px;vertical-align:middle;" onerror="this.style.display='none'"><span>${safeValue}</span>`;
        }
    },
    {
        field: 'asin',
        headerName: 'ASIN',
        width: 120,
        sortable: true,
        filterable: true,
        cellRenderer: (value) => {
            if (isPrivacyEnabledSync()) return PRIVACY_PLACEHOLDER;
            return value || '';
        }
    },
    { field: 'status', headerName: 'Status', width: 140, type: 'status', sortable: true, filterable: true },
    { field: 'productType', headerName: 'Product Type', width: 150, sortable: true, filterable: true },
    {
        field: 'brand',
        headerName: 'Brand',
        width: 140,
        sortable: true,
        filterable: true,
        cellRenderer: (value) => {
            if (isPrivacyEnabledSync()) return PRIVACY_PLACEHOLDER;
            return value || '';
        }
    },
    {
        field: 'title',
        headerName: 'Product Title',
        width: 260,
        sortable: true,
        filterable: true,
        cellRenderer: (value) => {
            if (isPrivacyEnabledSync()) return PRIVACY_PLACEHOLDER;
            return value || '';
        }
    },
    { field: 'price', headerName: 'Price', width: 100, type: 'currency', sortable: true, filterable: true },
    { field: 'sales', headerName: 'Sales', width: 100, type: 'number', sortable: true, filterable: true },
    { field: 'returns', headerName: 'Returns', width: 100, type: 'number', sortable: true, filterable: true },
    {
        field: 'returnRate',
        headerName: 'Return Rate',
        width: 110,
        sortable: true,
        filterable: true,
        cellRenderer: (value) => (value == null ? '' : `${value}%`)
    },
    { field: 'royalties', headerName: 'Royalties', width: 120, type: 'currency', sortable: true, filterable: true },
    { field: 'firstSold', headerName: 'First Sold', width: 140, type: 'date', sortable: true, filterable: true },
    { field: 'lastSold', headerName: 'Last Sold', width: 140, type: 'date', sortable: true, filterable: true },
    { field: 'bsr', headerName: 'BSR', width: 120, type: 'number', sortable: true, filterable: true },
    { field: 'firstPublished', headerName: 'First Published', width: 160, type: 'date', sortable: true, filterable: true },
    { field: 'lastUpdated', headerName: 'Last Updated', width: 160, type: 'date', sortable: true, filterable: true },
    { field: 'reviews', headerName: 'Reviews', width: 110, type: 'number', sortable: true, filterable: true },
    { field: 'designId', headerName: 'Design ID', width: 140, sortable: true, filterable: true }
];

const productsComponentState = {
    gridInstance: null,
    assetObservers: [],
    unloadHandler: null,
    gridRoot: null,
    resourcePromise: null
};

function ensureStylesheet(href) {
    return new Promise((resolve, reject) => {
        const existing = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))
            .find(link => link.href && link.href.includes(href));
        if (existing) {
            if (existing.sheet || existing.dataset.loaded === 'true') {
                resolve(existing);
                return;
            }
            existing.addEventListener('load', () => resolve(existing), { once: true });
            existing.addEventListener('error', () => reject(new Error(`Failed to load stylesheet ${href}`)), { once: true });
            return;
        }

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.onload = () => {
            link.dataset.loaded = 'true';
            resolve(link);
        };
        link.onerror = () => reject(new Error(`Failed to load stylesheet ${href}`));
        document.head.appendChild(link);
    });
}

function ensureScript(src) {
    return new Promise((resolve, reject) => {
        const existing = Array.from(document.querySelectorAll('script'))
            .find(script => script.src && script.src.includes(src));
        if (existing) {
            if (existing.dataset.loaded === 'true' || existing.readyState === 'complete') {
                resolve(existing);
                return;
            }
            existing.addEventListener('load', () => resolve(existing), { once: true });
            existing.addEventListener('error', () => reject(new Error(`Failed to load script ${src}`)), { once: true });
            return;
        }

        const script = document.createElement('script');
        script.src = src;
        script.async = false;
        script.onload = () => {
            script.dataset.loaded = 'true';
            resolve(script);
        };
        script.onerror = () => reject(new Error(`Failed to load script ${src}`));
        document.head.appendChild(script);
    });
}

async function ensureSnapGridResources() {
    if (!productsComponentState.resourcePromise) {
        productsComponentState.resourcePromise = (async () => {
            await Promise.all([
                ensureStylesheet(SNAP_GRID_CSS_PATH),
                ensureScript(CHUNKED_LOADER_SCRIPT_PATH),
                ensureScript(SNAP_GRID_SCRIPT_PATH)
            ]);

            if (typeof window.SnapGrid !== 'function') {
                throw new Error('SnapGrid engine failed to load');
            }
            if (typeof window.ChunkedDataLoader !== 'function') {
                throw new Error('ChunkedDataLoader failed to load');
            }
        })().catch(error => {
            productsComponentState.resourcePromise = null;
            throw error;
        });
    }

    return productsComponentState.resourcePromise;
}

function cloneColumnDefinitions() {
    return PRODUCT_GRID_COLUMNS.map(col => ({ ...col }));
}

function belongsToSnapGrid(node, gridRoot) {
    if (!(node instanceof Element)) {
        return false;
    }
    if (gridRoot && gridRoot.contains(node)) {
        return true;
    }
    if (typeof node.className === 'string' && node.className.indexOf('snap-grid') !== -1) {
        return true;
    }
    const relevantSelectors = [
        '.snap-grid-loading-overlay',
        '.snap-grid-column-menu',
        '.snap-grid-filter-menu',
        '.snap-grid-export-dropdown',
        '.snap-grid-delete-menu',
        '.snap-grid-filter-popup',
        '.snap-grid-dropdown',
        '.snap-grid-notification',
        '.snap-datepicker-popup',
        // Popups (delete/save) appended to document.body by SnapGrid
        '.popup-overlay'
    ];
    return relevantSelectors.some(selector => node.closest(selector));
}

function normalizeAssetPath(src) {
    if (!src) return src;
    if (src.startsWith(SNAP_GRID_ASSET_PREFIX)) {
        return src;
    }
    if (src.startsWith('data:') || src.startsWith('http://') || src.startsWith('https://') || src.startsWith('//')) {
        return src;
    }
    if (src.startsWith('./')) {
        src = src.substring(2);
    }
    if (!src.startsWith('assets/')) {
        return src;
    }
    const relativePath = src.substring('assets/'.length);
    return `${SNAP_GRID_ASSET_PREFIX}${relativePath}`;
}

function rewriteAssetSource(img, gridRoot) {
    if (!(img instanceof Element)) return;
    if (!belongsToSnapGrid(img, gridRoot)) return;

    const currentSrc = img.getAttribute('src');
    const normalizedSrc = normalizeAssetPath(currentSrc);
    if (normalizedSrc && normalizedSrc !== currentSrc) {
        img.setAttribute('src', normalizedSrc);
    }
}

function applyAssetPrefix(root, gridRoot) {
    if (!root) return;
    const targets = [];

    if (root instanceof Element && root.tagName === 'IMG') {
        targets.push(root);
    } else if (root instanceof Element) {
        targets.push(...root.querySelectorAll('img'));
    }

    targets.forEach(img => rewriteAssetSource(img, gridRoot));
}

function observeGridAssets(gridRoot) {
    const observers = [];
    if (!gridRoot) {
        return observers;
    }

    const mutationHandler = (mutations) => {
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (node instanceof Element) {
                        applyAssetPrefix(node, gridRoot);
                    }
                });
            }
            if (mutation.type === 'attributes' && mutation.target instanceof Element && mutation.target.tagName === 'IMG') {
                rewriteAssetSource(mutation.target, gridRoot);
            }
        });
    };

    const containerObserver = new MutationObserver(mutationHandler);
    containerObserver.observe(gridRoot, { childList: true, subtree: true, attributes: true, attributeFilter: ['src'] });
    observers.push(containerObserver);

    // Also observe document.body for SnapGrid popups appended outside grid root
    const bodyObserver = new MutationObserver((mutations) => {
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (!(node instanceof Element)) return;
                    // Only handle known SnapGrid overlays/popups
                    if (node.classList.contains('popup-overlay') || node.querySelector('.popup-overlay')) {
                        applyAssetPrefix(node, gridRoot);
                    }
                });
            }
            if (mutation.type === 'attributes' && mutation.target instanceof Element && mutation.target.tagName === 'IMG') {
                const src = mutation.target.getAttribute('src') || '';
                if (src.startsWith('assets/')) {
                    rewriteAssetSource(mutation.target, gridRoot);
                }
            }
        });
    });
    bodyObserver.observe(document.body, { childList: true, subtree: true, attributes: true, attributeFilter: ['src'] });
    observers.push(bodyObserver);

    return observers;
}

function stopObservingAssets() {
    productsComponentState.assetObservers.forEach(observer => observer.disconnect());
    productsComponentState.assetObservers = [];
}

function destroyExistingGrid() {
    if (productsComponentState.gridInstance) {
        const grid = productsComponentState.gridInstance;
        try {
            if (typeof grid._cancelChunkedLoading === 'function') {
                grid._cancelChunkedLoading();
            }
        } catch (error) {
            console.warn('Failed to cancel SnapGrid chunk loading:', error);
        }
        try {
            grid.destroy();
        } catch (error) {
            console.warn('Failed to destroy SnapGrid instance:', error);
        }
    }

    productsComponentState.gridInstance = null;
    productsComponentState.gridRoot = null;
    stopObservingAssets();

    if (productsComponentState.unloadHandler) {
        window.removeEventListener('componentUnloaded', productsComponentState.unloadHandler);
        productsComponentState.unloadHandler = null;
    }
}

function attachUnloadHandler() {
    if (productsComponentState.unloadHandler) {
        return;
    }
    const handler = (event) => {
        const componentName = event && event.detail && event.detail.component;
        if (componentName === 'products') {
            destroyExistingGrid();
        }
    };
    window.addEventListener('componentUnloaded', handler);
    productsComponentState.unloadHandler = handler;
}

function renderError(mainContent, message) {
    if (!mainContent) return;
    const errorContainer = document.createElement('div');
    errorContainer.className = 'error-container';
    errorContainer.innerHTML = `
        <h1>Products Grid Error</h1>
        <p>${message}</p>
        <p>Please refresh or contact support if the issue persists.</p>
    `;
    mainContent.appendChild(errorContainer);
}

function createProductsLayout(mainContent) {
    const pageHeader = typeof window.createPageHeader === 'function'
        ? window.createPageHeader('Products')
        : (() => {
            const header = document.createElement('div');
            header.className = 'page-header';
            const title = document.createElement('h1');
            title.textContent = 'Products';
            header.appendChild(title);
            return header;
        })();

    const { container: headerWithActions, privacyToggleBtn, refreshBtn } = ensureHeaderActions(pageHeader);

    const productsContent = document.createElement('div');
    productsContent.className = 'products-content';
    productsContent.style.cssText = `
        width: 100%;
        padding-top: var(--page-header-height, 0px);
        transition: padding-top 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    `;

    const gridContainer = document.createElement('div');
    gridContainer.className = 'products-grid-container';

    const gridElement = document.createElement('div');
    gridElement.className = 'products-grid';
    gridElement.id = 'productsSnapGrid';

    gridContainer.appendChild(gridElement);
    productsContent.appendChild(gridContainer);

    mainContent.appendChild(headerWithActions);
    mainContent.appendChild(productsContent);

    return { gridElement, privacyToggleBtn, refreshBtn };
}

function ensureHeaderActions(pageHeader) {
    if (!pageHeader) {
        return { container: pageHeader, privacyToggleBtn: null, refreshBtn: null };
    }

    let actionsContainer = pageHeader.querySelector('.page-header-actions');
    if (!actionsContainer) {
        actionsContainer = document.createElement('div');
        actionsContainer.className = 'page-header-actions';
        pageHeader.appendChild(actionsContainer);
    } else {
        actionsContainer.innerHTML = '';
    }

    const refreshBtn = document.createElement('button');
    refreshBtn.className = 'dashboard-refresh-btn grid-refresh-btn';
    refreshBtn.type = 'button';
    refreshBtn.setAttribute('aria-label', 'Refresh products grid');
    refreshBtn.setAttribute('data-tooltip', 'Refresh Grid');
    refreshBtn.innerHTML = `
        <div class="refresh-inner">
            <div class="refresh-icon">
                <img src="./assets/refresh-dashboard-ic.svg" alt="Refresh Grid">
            </div>
        </div>
    `;

    // Generate privacy toggle HTML - with fallback if dashboard not loaded
    const privacyHTML = typeof window.getPrivacyModeHTML === 'function'
        ? window.getPrivacyModeHTML()
        : getPrivacyToggleFallbackHTML();

    const privacyMount = document.createElement('div');
    privacyMount.innerHTML = privacyHTML;

    actionsContainer.appendChild(refreshBtn);
    actionsContainer.appendChild(privacyMount);

    const privacyToggleBtn = privacyMount.querySelector('.privacy-toggle-btn');
    return { container: pageHeader, privacyToggleBtn, refreshBtn };
}

function getPrivacyToggleFallbackHTML() {
    const isEnabled = localStorage.getItem('privacyMode') === 'enabled';
    return `
        <div class="privacy-mode">
            <button class="privacy-toggle-btn" aria-label="Toggle privacy mode" data-tooltip="${isEnabled ? 'Display all data' : 'Hide all sensitive data'}">
                <div class="toggle toggle-off ${!isEnabled ? 'active' : ''}">
                    <div class="privacy-icon">
                        <img src="./assets/${!isEnabled ? 'privacy-off-active.svg' : 'privacy-off-inactive.svg'}" alt="Privacy Off" />
                    </div>
                </div>
                <div class="toggle toggle-on ${isEnabled ? 'active' : ''}">
                    <div class="privacy-icon">
                        <img src="./assets/${isEnabled ? 'privacy-on-active.svg' : 'privacy-on-inactive.svg'}" alt="Privacy On" />
                    </div>
                </div>
            </button>
        </div>
    `;
}

function applyHeaderInteractions({ privacyToggleBtn, refreshBtn }) {
    if (privacyToggleBtn) {
        setupPrivacySync(privacyToggleBtn);
    }
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            triggerGridRefresh(refreshBtn);
        });
    }
    // Tie refresh button animation to grid loading state (initial + future changes)
    if (refreshBtn) {
        bindRefreshAnimationToGridLoading(refreshBtn);
    }
}

function bindRefreshAnimationToGridLoading(refreshBtn) {
    const update = () => {
        const spinning = !!(productsComponentState.gridInstance && productsComponentState.gridInstance.isLoadingActive);
        refreshBtn.classList.toggle('refreshing', spinning);
        refreshBtn.disabled = spinning;
    };
    update();

    const container = productsComponentState.gridInstance && productsComponentState.gridInstance.container;
    if (!container) return;

    const mo = new MutationObserver(() => update());
    mo.observe(container, { childList: true, subtree: true, attributes: true, attributeFilter: ['style', 'class'] });
    productsComponentState.assetObservers.push(mo);
}

function setupPrivacyMaskingObserver(gridRoot) {
    if (!gridRoot) return;

    let debounceTimer = null;
    const observer = new MutationObserver(() => {
        const enabled = isPrivacyEnabled();
        if (enabled) {
            // Debounce to avoid excessive calls during rapid DOM changes
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => applyPrivacyMasking(), 50);
        }
    });
    observer.observe(gridRoot, { childList: true, subtree: true });
    productsComponentState.assetObservers.push(observer);
}

function synchronizePrivacyState() {
    const isEnabled = isPrivacyEnabled();
    if (typeof window.updatePrivacyModeUI === 'function') {
        window.updatePrivacyModeUI();
    } else {
        updatePrivacyToggleUI(isEnabled);
    }
    document.body.classList.toggle('privacy-mode-enabled', !!isEnabled);
}

function updatePrivacyToggleUI(isEnabled) {
    const privacyToggleBtn = document.querySelector('.privacy-toggle-btn');
    if (!privacyToggleBtn) return;

    const toggles = privacyToggleBtn.querySelectorAll('.toggle');
    toggles.forEach(toggle => {
        if (toggle.classList.contains('toggle-off')) {
            toggle.classList.toggle('active', !isEnabled);
            const img = toggle.querySelector('img');
            if (img) img.src = `./assets/${!isEnabled ? 'privacy-off-active.svg' : 'privacy-off-inactive.svg'}`;
        } else if (toggle.classList.contains('toggle-on')) {
            toggle.classList.toggle('active', isEnabled);
            const img = toggle.querySelector('img');
            if (img) img.src = `./assets/${isEnabled ? 'privacy-on-active.svg' : 'privacy-on-inactive.svg'}`;
        }
    });

    privacyToggleBtn.setAttribute('data-tooltip', isEnabled ? 'Display all data' : 'Hide all sensitive data');
}

function setupPrivacySync(privacyToggleBtn) {
    const handleToggle = () => {
        const enable = !isPrivacyEnabled();
        togglePrivacy(enable);
        synchronizePrivacyState();
        applyPrivacyMasking();
        try { window.dispatchEvent(new CustomEvent('privacyModeChanged')); } catch (_) {}
    };

    privacyToggleBtn.addEventListener('click', handleToggle);

    window.addEventListener('privacyModeChanged', () => {
        synchronizePrivacyState();
        applyPrivacyMasking();
    });

    synchronizePrivacyState();
}

function isPrivacyEnabled() {
    if (typeof window.isPrivacyModeEnabled === 'function') {
        return window.isPrivacyModeEnabled();
    }
    return localStorage.getItem('privacyMode') === 'enabled';
}

function togglePrivacy(enable) {
    if (typeof window.togglePrivacyMode === 'function') {
        window.togglePrivacyMode(enable);
    } else {
        localStorage.setItem('privacyMode', enable ? 'enabled' : 'disabled');
        document.body.classList.toggle('privacy-mode-enabled', enable);
    }
}

function triggerGridRefresh(refreshBtn) {
    if (!document.getElementById('productsSnapGrid')) return;
    if (refreshBtn.classList.contains('refreshing')) return;

    refreshBtn.classList.add('refreshing');
    refreshBtn.disabled = true;

    // Destroy current grid and re-initialize a fresh one in the same container
    const gridEl = document.getElementById('productsSnapGrid');
    destroyExistingGrid();
    initializeGridInstance(gridEl);

    // After re-init, tie the button to loading state and ensure masking is applied when done
    bindRefreshAnimationToGridLoading(refreshBtn);
    setupPrivacyMaskingObserver(gridEl);

    // Poll until grid loading ends, then finalize
    const finalize = () => {
        refreshBtn.classList.remove('refreshing');
        refreshBtn.disabled = false;
        applyPrivacyMasking();
    };

    const checkDone = () => {
        const grid = productsComponentState.gridInstance;
        if (!grid) return finalize();
        if (!grid.isLoadingActive) {
            finalize();
        } else {
            setTimeout(checkDone, 100);
        }
    };
    checkDone();
}

function showGridLoading() {
    const grid = productsComponentState.gridInstance;
    if (grid && typeof grid.showLoadingState === 'function') {
        grid.showLoadingState(true);
    }
}

function hideGridLoading() {
    const grid = productsComponentState.gridInstance;
    if (grid && typeof grid.hideLoadingState === 'function') {
        grid.hideLoadingState();
    }
}

async function reloadGridData() {
    const grid = productsComponentState.gridInstance;
    if (!grid) return;

    const dataGenerator = createDataGenerator();
    const chunkOptions = { chunkSize: 10000, delayBetweenChunks: 16, maxMemoryChunks: 5, enableMemoryManagement: false };
    const initialChunkSize = Math.min(chunkOptions.chunkSize, TOTAL_PRODUCT_ROWS);
    const initialData = initialChunkSize > 0 ? dataGenerator(initialChunkSize, 0) : [];

    await grid.setRowData(initialData);
    if (TOTAL_PRODUCT_ROWS > initialData.length) {
        await grid.loadDataInChunks(TOTAL_PRODUCT_ROWS, dataGenerator, chunkOptions);
    }
}

function applyPrivacyMasking() {
    const grid = productsComponentState.gridInstance;
    if (!grid) return;

    const isEnabled = isPrivacyEnabled();
    const placeholderText = 'Privacy Mode is enabled.';

    // Get all visible rows from the grid
    const gridContainer = grid.container;
    if (!gridContainer) return;

    const allRows = gridContainer.querySelectorAll('.snap-grid-row');

    allRows.forEach(rowElement => {
        if (!rowElement) return;

        // Find cells by data-field attribute
        const brandCell = rowElement.querySelector('[data-field="brand"] .snap-grid-cell-content');
        const titleCell = rowElement.querySelector('[data-field="title"] .snap-grid-cell-content');
        const asinCell = rowElement.querySelector('[data-field="asin"] .snap-grid-cell-content');
        const previewSquare = rowElement.querySelector('[data-field="preview"] .preview-square');

        if (isEnabled) {
            // Apply masking
            if (brandCell) {
                if (!brandCell.dataset.originalText) {
                    brandCell.dataset.originalText = brandCell.textContent;
                }
                brandCell.textContent = placeholderText;
            }
            if (titleCell) {
                if (!titleCell.dataset.originalText) {
                    titleCell.dataset.originalText = titleCell.textContent;
                }
                titleCell.textContent = placeholderText;
            }
            if (asinCell) {
                if (!asinCell.dataset.originalText) {
                    asinCell.dataset.originalText = asinCell.textContent;
                }
                asinCell.textContent = placeholderText;
            }
            if (previewSquare) {
                if (!previewSquare.dataset.originalHTML) {
                    previewSquare.dataset.originalHTML = previewSquare.innerHTML;
                }
                previewSquare.style.background = '#470CED';
                previewSquare.style.display = 'flex';
                previewSquare.style.alignItems = 'center';
                previewSquare.style.justifyContent = 'center';
                previewSquare.innerHTML = '<span class="privacy-icon"><img src="./assets/privacy-mode-ic.svg" alt="Privacy Enabled"></span>';
            }
        } else {
            // Restore original content
            if (brandCell && brandCell.dataset.originalText) {
                brandCell.textContent = brandCell.dataset.originalText;
                delete brandCell.dataset.originalText;
            }
            if (titleCell && titleCell.dataset.originalText) {
                titleCell.textContent = titleCell.dataset.originalText;
                delete titleCell.dataset.originalText;
            }
            if (asinCell && asinCell.dataset.originalText) {
                asinCell.textContent = asinCell.dataset.originalText;
                delete asinCell.dataset.originalText;
            }
            if (previewSquare && previewSquare.dataset.originalHTML) {
                previewSquare.innerHTML = previewSquare.dataset.originalHTML;
                previewSquare.style.background = '';
                previewSquare.style.display = '';
                previewSquare.style.alignItems = '';
                previewSquare.style.justifyContent = '';
                delete previewSquare.dataset.originalHTML;
            }
        }
    });
}

function createDataGenerator() {
    if (typeof window.ChunkedDataLoader === 'function' && typeof window.ChunkedDataLoader.createDataGenerator === 'function') {
        return window.ChunkedDataLoader.createDataGenerator({ seed: 1234 });
    }
    return (count, offset) => {
        const rows = [];
        // Status values matching SnapGrid's getStatusStyle() mapping
        const statuses = ['Live', 'Draft', 'Processing', 'Under Review', 'Locked', 'Rejected'];
        for (let i = 0; i < count; i++) {
            const index = offset + i;
            rows.push({
                marketplace: 'US',
                asin: `B${String(index).padStart(9, '0')}`,
                status: statuses[index % statuses.length],
                productType: 'T-Shirt',
                brand: 'Snap Brand',
                title: `Snap Brand T-Shirt - Design ${index + 1}`,
                price: 19.99,
                sales: 0,
                returns: 0,
                returnRate: 0,
                royalties: 5.0,
                firstSold: '2024-01-01',
                lastSold: '2024-01-01',
                bsr: index + 1,
                firstPublished: '2024-01-01',
                lastUpdated: '2024-01-01',
                reviews: 0,
                designId: `DESIGN-${String(index).padStart(6, '0')}`
            });
        }
        return rows;
    };
}

function initializeGridInstance(gridElement) {
    const columns = cloneColumnDefinitions();
    const chunkOptions = {
        chunkSize: 10000,
        delayBetweenChunks: 16,
        maxMemoryChunks: 5,
        enableMemoryManagement: false
    };

    const dataGenerator = createDataGenerator();
    const initialChunkSize = Math.min(chunkOptions.chunkSize, TOTAL_PRODUCT_ROWS);
    const initialData = initialChunkSize > 0 ? dataGenerator(initialChunkSize, 0) : [];

    const grid = new window.SnapGrid(gridElement, {
        data: initialData,
        columns,
        checkboxSelection: true,
        headerCheckboxSelection: true,
        columnDragging: true,
        virtualScrolling: true,
        sortable: true,
        filterable: true,
        resizable: true,
        editable: false,
        showFooter: true
    });

    productsComponentState.gridInstance = grid;
    productsComponentState.gridRoot = gridElement;

    applyAssetPrefix(gridElement, gridElement);
    productsComponentState.assetObservers = observeGridAssets(gridElement);

    let firstChunkServed = initialData.length === 0;
    const chunkedGenerator = (count, offset) => {
        if (!firstChunkServed && offset === 0) {
            firstChunkServed = true;
            return initialData;
        }
        return dataGenerator(count, offset);
    };

    const beginChunkLoading = () => {
        if (TOTAL_PRODUCT_ROWS <= initialData.length) {
            return;
        }
        grid.loadDataInChunks(TOTAL_PRODUCT_ROWS, chunkedGenerator, chunkOptions)
            .catch(error => {
                console.error('SnapGrid chunked loading failed:', error);
            });
    };

    if (typeof window.requestIdleCallback === 'function') {
        window.requestIdleCallback(beginChunkLoading, { timeout: 200 });
    } else {
        requestAnimationFrame(() => beginChunkLoading());
    }

    return grid;
}

async function render() {
    const mainContent = document.querySelector('.main-content');
    if (!mainContent) {
        console.error('Main content container not found');
        return;
    }

    destroyExistingGrid();
    mainContent.innerHTML = '';

    try {
        await ensureSnapGridResources();
        const { gridElement, privacyToggleBtn, refreshBtn } = createProductsLayout(mainContent);
        initializeGridInstance(gridElement);
        attachUnloadHandler();
        applyHeaderInteractions({ privacyToggleBtn, refreshBtn });
        // Keep refresh button in sync with initial auto-loading
        if (refreshBtn) bindRefreshAnimationToGridLoading(refreshBtn);
        // Observe row mounts during loads to keep masking applied when enabled
        setupPrivacyMaskingObserver(gridElement);
        // Apply privacy masking on initial render - multiple attempts to catch rows as they render
        setTimeout(applyPrivacyMasking, 100);
        setTimeout(applyPrivacyMasking, 300);
        setTimeout(applyPrivacyMasking, 600);
        console.log('Products grid initialized with SnapGrid engine.');
    } catch (error) {
        console.error('Failed to render Products grid:', error);
        renderError(mainContent, error.message || 'An unexpected error occurred.');
    }
}

window.productsComponent = {
    render,
    destroy: destroyExistingGrid
};
