# Products Page Grid Enhancements

## Task Overview
Implement three improvements on the products page grid: fix warning icon asset loading, add a synchronized privacy toggle, and add a grid-specific refresh control.

## Tasks

- [ ] Task 1: Resolve `warning-ic.svg` loading path so it works in products grid and dashboard
- [ ] Task 2: Add privacy toggle to products header, sync state with dashboard, and apply privacy masking to grid rows
- [ ] Task 3: Add grid refresh button to products header with loading animation tied to grid refresh lifecycle
